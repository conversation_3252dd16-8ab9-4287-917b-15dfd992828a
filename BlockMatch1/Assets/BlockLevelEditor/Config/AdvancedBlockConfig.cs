#if UNITY_EDITOR

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEngine;
using Newtonsoft.Json;
using BlockLevelEditor.Core.Data;
using static BlockLevelEditor.Core.Data.BlockTypeDefinitions;

namespace BlockLevelEditor.Config
{
    /// <summary>
    /// 特殊方块配置管理器 - 读取和管理ingame_advancedblock.json配置，并适配到统一的方块分类体系
    /// </summary>
    public static class AdvancedBlockConfig
    {
        private static Dictionary<int, AdvancedBlockInfo> _blockInfos;
        private static Dictionary<int, EntityBlockInfo> _entityBlocks;
        private static Dictionary<int, OverlayBlockInfo> _overlayBlocks;
        private static bool _isLoaded = false;
        private static bool _isLoading = false;

        private const string CONFIG_PATH = "Assets/Res/Configs/InGame/ingame_advancedblock.json";

        /// <summary>
        /// 特殊方块信息
        /// </summary>
        [System.Serializable]
        public class AdvancedBlockInfo
        {
            public int id;
            public int blockType;
            public int clearLayer;
            public int[] blockSize;
            public string prefabName;
        }

        /// <summary>
        /// 加载配置文件并适配到统一的方块分类体系
        /// </summary>
        public static void LoadConfig()
        {
            if (_isLoaded || _isLoading)
                return;

            _isLoading = true;

            _blockInfos = new Dictionary<int, AdvancedBlockInfo>();
            _entityBlocks = new Dictionary<int, EntityBlockInfo>();
            _overlayBlocks = new Dictionary<int, OverlayBlockInfo>();

            try
            {
                // 首先加载默认配置
                LoadDefaultConfigs();

                // 然后尝试加载JSON配置文件
                if (File.Exists(CONFIG_PATH))
                {
                    LoadJsonConfig();
                }
                else
                {
                    Debug.LogWarning($"特殊方块配置文件不存在: {CONFIG_PATH}，使用默认配置");
                }

                Debug.Log($"已加载配置: {_blockInfos.Count} 个原始配置, {_entityBlocks.Count} 个实体方块, {_overlayBlocks.Count} 个覆盖物");
                _isLoaded = true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"加载特殊方块配置失败: {ex.Message}");
                LoadDefaultConfigs(); // 失败时使用默认配置
                _isLoaded = true;
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// 加载默认配置
        /// </summary>
        private static void LoadDefaultConfigs()
        {
            // 加载默认实体方块配置
            foreach (var entityBlock in DefaultEntityBlocks)
            {
                _entityBlocks[entityBlock.id] = entityBlock;
            }

            // 加载默认覆盖物配置
            foreach (var overlayBlock in DefaultOverlayBlocks)
            {
                _overlayBlocks[overlayBlock.id] = overlayBlock;
            }
        }

        /// <summary>
        /// 加载JSON配置文件
        /// </summary>
        private static void LoadJsonConfig()
        {
            string json = File.ReadAllText(CONFIG_PATH);
            var blockInfos = JsonConvert.DeserializeObject<AdvancedBlockInfo[]>(json);

            if (blockInfos != null)
            {
                foreach (var info in blockInfos)
                {
                    _blockInfos[info.id] = info;

                    // 适配到统一的方块分类体系
                    AdaptToUnifiedSystem(info);
                }
            }
        }

        /// <summary>
        /// 将原始配置适配到统一的方块分类体系
        /// </summary>
        private static void AdaptToUnifiedSystem(AdvancedBlockInfo originalInfo)
        {
            switch (originalInfo.blockType)
            {
                case 1: // 多层块
                case 2: // 固定块
                    AdaptToEntityBlock(originalInfo);
                    break;
                case 3: // 覆盖块
                    AdaptToOverlayBlock(originalInfo);
                    break;
                case 4: // 触发块（暂时作为实体方块处理）
                    AdaptToEntityBlock(originalInfo);
                    break;
            }
        }

        /// <summary>
        /// 适配为实体方块
        /// </summary>
        private static void AdaptToEntityBlock(AdvancedBlockInfo originalInfo)
        {
            var entityBlock = new EntityBlockInfo
            {
                id = originalInfo.id,
                name = GetBlockDisplayNameDirect(originalInfo.id), // 使用直接方法避免循环调用
                entityType = originalInfo.blockType == 2 ? EntityBlockType.Fixed : EntityBlockType.MultiLayer,
                blockSize = originalInfo.blockSize ?? new int[] { 1, 1 },
                minLayers = originalInfo.blockType == 2 ? 1 : 1, // 固定方块只有1层
                maxLayers = originalInfo.clearLayer > 0 ? originalInfo.clearLayer : (originalInfo.id == 302 ? 4 : 3),
                canDestroy = originalInfo.blockType != 2, // 固定方块不可消除
                prefabName = originalInfo.prefabName ?? $"Block{originalInfo.id}"
            };

            _entityBlocks[originalInfo.id] = entityBlock;
        }

        /// <summary>
        /// 适配为覆盖物方块
        /// </summary>
        private static void AdaptToOverlayBlock(AdvancedBlockInfo originalInfo)
        {
            var overlayBlock = new OverlayBlockInfo
            {
                id = originalInfo.id,
                name = GetBlockDisplayNameDirect(originalInfo.id), // 使用直接方法避免循环调用
                overlayType = originalInfo.id == 501 ? OverlayBlockType.SingleLayer : OverlayBlockType.MultiLayer,
                minLayers = 1,
                maxLayers = originalInfo.clearLayer > 0 ? originalInfo.clearLayer : (originalInfo.id == 502 ? 3 : 1),
                canDestroy = true,
                prefabName = originalInfo.prefabName ?? $"Overlay{originalInfo.id}",
                renderOrder = originalInfo.id == 501 ? 1 : 2 // 树叶在下，冰块在上
            };

            _overlayBlocks[originalInfo.id] = overlayBlock;
        }
        /// <summary>
        /// 获取方块的显示名称（直接方法，不会触发循环调用）
        /// </summary>
        private static string GetBlockDisplayNameDirect(int blockId)
        {
            switch (blockId)
            {
                case 301: return "木箱";
                case 302: return "小猫";
                case 401: return "鸟窝";
                case 501: return "树叶";
                case 502: return "冰块";
                case 701: return "触发";
                default: return $"Block{blockId}";
            }
        }

        // ========== 新的统一API ==========

        /// <summary>
        /// 获取实体方块信息
        /// </summary>
        public static EntityBlockInfo GetEntityBlock(int blockId)
        {
            if (!_isLoaded)
                LoadConfig();

            _entityBlocks.TryGetValue(blockId, out var info);
            return info;
        }

        /// <summary>
        /// 获取覆盖物方块信息
        /// </summary>
        public static OverlayBlockInfo GetOverlayBlock(int blockId)
        {
            if (!_isLoaded)
                LoadConfig();

            _overlayBlocks.TryGetValue(blockId, out var info);
            return info;
        }

        /// <summary>
        /// 获取所有实体方块
        /// </summary>
        public static List<EntityBlockInfo> GetAllEntityBlocks()
        {
            if (!_isLoaded)
                LoadConfig();

            return _entityBlocks.Values.ToList();
        }

        /// <summary>
        /// 获取所有覆盖物方块
        /// </summary>
        public static List<OverlayBlockInfo> GetAllOverlayBlocks()
        {
            if (!_isLoaded)
                LoadConfig();

            return _overlayBlocks.Values.ToList();
        }
    }
}

#endif
