#if UNITY_EDITOR

using System;
using System.Collections.Generic;
using UnityEngine;
using BlockLevelEditor.Core.Data;
using BlockLevelEditor.Config;

namespace BlockLevelEditor.Core.Manager
{
    /// <summary>
    /// 2x2大方块管理器 - 处理2x2方块的放置、移除和验证
    /// </summary>
    public static class BigBlockManager
    {
        /// <summary>
        /// 检查指定位置是否被2x2方块占用
        /// </summary>
        public static bool IsPositionOccupiedBy2x2Block(LevelData levelData, int x, int y)
        {
            // 首先检查该位置是否有2x2方块的TileData
            var tile = levelData.GetTile(x, y);
            if (tile != null && tile.Is2x2Part)
                return true;

            // 如果该位置没有TileData，检查是否被其他位置的2x2主方块占用
            foreach (var otherTile in levelData.tiles)
            {
                if (otherTile != null && otherTile.Is2x2Master)
                {
                    var occupiedPositions = otherTile.GetOccupiedPositions();
                    foreach (var occupiedPos in occupiedPositions)
                    {
                        if (occupiedPos.x == x && occupiedPos.y == y)
                        {
                            return true; // 该位置被2x2方块占用
                        }
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 检查指定位置是否可以放置2x2方块
        /// </summary>
        public static bool CanPlace2x2Block(LevelData levelData, int startX, int startY)
        {
            // 检查边界
            if (startX < 0 || startX + 1 >= GridManager.GRID_SIZE ||
                startY < 0 || startY + 1 >= GridManager.GRID_SIZE)
            {
                return false;
            }

            // 目标2x2区域的4个位置
            var targetPositions = new Vector2Int[]
            {
                new Vector2Int(startX, startY),         // 左上
                new Vector2Int(startX + 1, startY),     // 右上
                new Vector2Int(startX, startY + 1),     // 左下
                new Vector2Int(startX + 1, startY + 1)  // 右下
            };

            // 检查目标区域的每个位置
            foreach (var pos in targetPositions)
            {
                var tile = levelData.GetTile(pos.x, pos.y);
                if (tile != null && !tile.IsEmpty)
                {
                    // 如果是固定方块，不能放置
                    if (tile.isFixed)
                        return false;

                    // 如果是特殊方块，不能放置
                    if (tile.HasAdvancedBlock)
                        return false;
                }
            }

            // 检查是否有其他2x2方块的占用区域与目标区域重叠
            foreach (var tile in levelData.tiles)
            {
                if (tile != null && tile.Is2x2Master)
                {
                    var occupiedPositions = tile.GetOccupiedPositions();
                    foreach (var occupiedPos in occupiedPositions)
                    {
                        foreach (var targetPos in targetPositions)
                        {
                            if (occupiedPos.x == targetPos.x && occupiedPos.y == targetPos.y)
                            {
                                return false; // 有重叠，不能放置
                            }
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// 在指定位置放置2x2方块
        /// </summary>
        public static bool Place2x2Block(LevelData levelData, int startX, int startY, int blockId, int layers)
        {
            if (!CanPlace2x2Block(levelData, startX, startY))
                return false;

            // 只在点击的位置创建一个方块数据，不在其他3个位置创建数据
            var tile = new TileData(startX, startY);
            tile.SetAs2x2Block(blockId, layers, true);
            levelData.SetTile(startX, startY, tile);

            // 标记其他3个位置为被占用（但不创建实际的TileData）
            // 这样可以防止在这些位置放置其他方块，但JSON中不会有多余的数据
            var positions = new Vector2Int[]
            {
                new Vector2Int(startX + 1, startY),     // 右上
                new Vector2Int(startX, startY + 1),     // 左下
                new Vector2Int(startX + 1, startY + 1)  // 右下
            };

            // 注意：这里不再创建从属方块的TileData，只在内存中标记占用
            // 游戏逻辑需要根据主方块的位置和2x2属性来判断整个2x2区域的占用情况

            return true;
        }

        /// <summary>
        /// 移除2x2方块
        /// </summary>
        public static bool Remove2x2Block(LevelData levelData, int x, int y)
        {
            var tile = levelData.GetTile(x, y);
            if (tile == null || !tile.Is2x2Part)
                return false;

            Vector2Int masterPos;
            if (tile.Is2x2Master)
            {
                masterPos = new Vector2Int(x, y);
            }
            else
            {
                masterPos = tile.masterPosition;
            }

            // 移除所有相关的瓦片
            var positions = new Vector2Int[]
            {
                masterPos,                                    // 左上
                new Vector2Int(masterPos.x + 1, masterPos.y), // 右上
                new Vector2Int(masterPos.x, masterPos.y + 1), // 左下
                new Vector2Int(masterPos.x + 1, masterPos.y + 1) // 右下
            };

            foreach (var pos in positions)
            {
                levelData.RemoveTile(pos.x, pos.y);
            }

            return true;
        }

        /// <summary>
        /// 获取2x2方块的主方块位置
        /// </summary>
        public static Vector2Int? GetMasterPosition(LevelData levelData, int x, int y)
        {
            var tile = levelData.GetTile(x, y);
            if (tile == null || !tile.Is2x2Part)
                return null;

            if (tile.Is2x2Master)
                return new Vector2Int(x, y);
            else
                return tile.masterPosition;
        }

        /// <summary>
        /// 获取2x2方块占用的所有位置
        /// </summary>
        public static Vector2Int[] Get2x2Positions(Vector2Int masterPos)
        {
            return new Vector2Int[]
            {
                masterPos,                                    // 左上
                new Vector2Int(masterPos.x + 1, masterPos.y), // 右上
                new Vector2Int(masterPos.x, masterPos.y + 1), // 左下
                new Vector2Int(masterPos.x + 1, masterPos.y + 1) // 右下
            };
        }

        /// <summary>
        /// 验证2x2方块的完整性
        /// </summary>
        public static bool Validate2x2Block(LevelData levelData, int masterX, int masterY, out List<string> errors)
        {
            errors = new List<string>();
            var masterTile = levelData.GetTile(masterX, masterY);

            if (masterTile == null || !masterTile.Is2x2Master)
            {
                errors.Add($"位置({masterX},{masterY})不是2x2方块的主方块");
                return false;
            }

            var positions = Get2x2Positions(new Vector2Int(masterX, masterY));
            bool isValid = true;

            for (int i = 0; i < positions.Length; i++)
            {
                var pos = positions[i];
                var tile = levelData.GetTile(pos.x, pos.y);

                if (tile == null)
                {
                    errors.Add($"2x2方块缺少位置({pos.x},{pos.y})的瓦片");
                    isValid = false;
                    continue;
                }

                if (!tile.Is2x2Part)
                {
                    errors.Add($"位置({pos.x},{pos.y})的瓦片不是2x2方块的一部分");
                    isValid = false;
                    continue;
                }

                if (i == 0) // 主方块
                {
                    if (!tile.Is2x2Master)
                    {
                        errors.Add($"位置({pos.x},{pos.y})应该是主方块但不是");
                        isValid = false;
                    }
                }
                else // 从属方块
                {
                    if (tile.Is2x2Master)
                    {
                        errors.Add($"位置({pos.x},{pos.y})应该是从属方块但是主方块");
                        isValid = false;
                    }

                    if (tile.masterPosition.x != masterX || tile.masterPosition.y != masterY)
                    {
                        errors.Add($"位置({pos.x},{pos.y})的主方块位置不正确");
                        isValid = false;
                    }
                }

                // 检查方块ID和层数是否一致
                if (tile.advancedBlockId != masterTile.advancedBlockId)
                {
                    errors.Add($"位置({pos.x},{pos.y})的方块ID与主方块不一致");
                    isValid = false;
                }

                if (tile.blockLayers != masterTile.blockLayers)
                {
                    errors.Add($"位置({pos.x},{pos.y})的层数与主方块不一致");
                    isValid = false;
                }
            }

            return isValid;
        }

        /// <summary>
        /// 检查指定方块ID是否为2x2方块
        /// </summary>
        public static bool Is2x2BlockId(int blockId)
        {
            var entityBlock = UnifiedBlockConfig.GetEntityBlock(blockId);
            return entityBlock != null && entityBlock.Is2x2;
        }

        /// <summary>
        /// 获取所有2x2方块的主方块位置
        /// </summary>
        public static List<Vector2Int> GetAll2x2MasterPositions(LevelData levelData)
        {
            var masterPositions = new List<Vector2Int>();

            for (int y = 0; y < GridManager.GRID_SIZE; y++)
            {
                for (int x = 0; x < GridManager.GRID_SIZE; x++)
                {
                    var tile = levelData.GetTile(x, y);
                    if (tile != null && tile.Is2x2Master)
                    {
                        masterPositions.Add(new Vector2Int(x, y));
                    }
                }
            }

            return masterPositions;
        }
    }
}

#endif
