#if UNITY_EDITOR

using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using BlockLevelEditor.Core.Data;

namespace BlockLevelEditor.Core.Manager
{
    /// <summary>
    /// 关卡目标管理器 - 管理关卡目标的增删改查操作
    /// </summary>
    public class LevelTargetManager
    {
        private LevelTargetData _targetData;
        private LevelEditorManager _editorManager;

        // 事件
        public event Action<LevelTargetType> OnTargetTypeChanged;
        public event Action<int> OnTargetScoreChanged;
        public event Action<List<TargetItem>> OnTargetItemsChanged;

        public LevelTargetManager(LevelEditorManager editorManager)
        {
            _editorManager = editorManager;
        }

        /// <summary>
        /// 设置目标数据
        /// </summary>
        public void SetTargetData(LevelTargetData targetData)
        {
            _targetData = targetData;
        }

        /// <summary>
        /// 获取当前目标数据
        /// </summary>
        public LevelTargetData GetTargetData()
        {
            return _targetData;
        }

        /// <summary>
        /// 切换目标类型
        /// </summary>
        public bool SwitchTargetType(LevelTargetType newType)
        {
            if (_targetData == null)
            {
                Debug.LogError("目标数据为空，无法切换类型");
                return false;
            }

            var oldType = _targetData.TargetType;
            if (oldType == newType)
                return true; // 类型相同，无需切换

            // 切换类型
            _targetData.TargetType = newType;

            // 根据新类型初始化数据
            if (newType == LevelTargetType.Score)
            {
                // 切换到分数关，清空收集目标
                _targetData.ClearTargetItems();
                if (_targetData.targetScore <= 0)
                {
                    _targetData.targetScore = 1000; // 设置默认分数
                }
            }
            else if (newType == LevelTargetType.Collection)
            {
                // 切换到收集关，重置分数
                _targetData.targetScore = 0;
                // 保留现有的收集目标
            }

            // 触发事件
            OnTargetTypeChanged?.Invoke(newType);
            _editorManager?.SetDirty(true);

            Debug.Log($"目标类型已切换: {oldType} -> {newType}");
            return true;
        }

        /// <summary>
        /// 设置目标分数
        /// </summary>
        public bool SetTargetScore(int score)
        {
            if (_targetData == null)
            {
                Debug.LogError("目标数据为空，无法设置分数");
                return false;
            }

            if (score <= 0)
            {
                Debug.LogWarning("目标分数必须大于0");
                return false;
            }

            if (_targetData.targetScore == score)
                return true; // 分数相同，无需更新

            _targetData.targetScore = score;
            OnTargetScoreChanged?.Invoke(score);
            _editorManager?.SetDirty(true);

            Debug.Log($"目标分数已设置: {score}");
            return true;
        }

        /// <summary>
        /// 添加收集目标
        /// </summary>
        public bool AddTargetItem(int type, int count)
        {
            if (_targetData == null)
            {
                Debug.LogError("目标数据为空，无法添加目标");
                return false;
            }

            if (!_targetData.IsCollectionLevel)
            {
                Debug.LogWarning("当前不是收集关，无法添加收集目标");
                return false;
            }

            // 验证目标类型
            if (!TargetTypeDefinitions.IsValidTargetType(type))
            {
                Debug.LogWarning($"无效的目标类型: {type}");
                return false;
            }

            // 检查是否已存在
            if (_targetData.targetItems.Any(item => item.type == type))
            {
                Debug.LogWarning($"目标类型 {type} 已存在");
                return false;
            }

            // 检查数量限制
            if (_targetData.targetItems.Count >= 4)
            {
                Debug.LogWarning("最多只能设置4个收集目标");
                return false;
            }

            if (count <= 0)
            {
                Debug.LogWarning("目标数量必须大于0");
                return false;
            }

            // 添加目标
            var success = _targetData.AddTargetItem(type, count);
            if (success)
            {
                OnTargetItemsChanged?.Invoke(_targetData.targetItems);
                _editorManager?.SetDirty(true);
                Debug.Log($"已添加收集目标: {TargetTypeDefinitions.GetTargetTypeName(type)} x{count}");
            }

            return success;
        }

        /// <summary>
        /// 移除收集目标
        /// </summary>
        public bool RemoveTargetItem(int type)
        {
            if (_targetData == null)
            {
                Debug.LogError("目标数据为空，无法移除目标");
                return false;
            }

            var success = _targetData.RemoveTargetItem(type);
            if (success)
            {
                OnTargetItemsChanged?.Invoke(_targetData.targetItems);
                _editorManager?.SetDirty(true);
                Debug.Log($"已移除收集目标: {TargetTypeDefinitions.GetTargetTypeName(type)}");
            }

            return success;
        }

        /// <summary>
        /// 更新收集目标数量
        /// </summary>
        public bool UpdateTargetItemCount(int type, int count)
        {
            if (_targetData == null)
            {
                Debug.LogError("目标数据为空，无法更新目标数量");
                return false;
            }

            if (count <= 0)
            {
                Debug.LogWarning("目标数量必须大于0");
                return false;
            }

            var success = _targetData.UpdateTargetItemCount(type, count);
            if (success)
            {
                OnTargetItemsChanged?.Invoke(_targetData.targetItems);
                _editorManager?.SetDirty(true);
                Debug.Log($"已更新收集目标数量: {TargetTypeDefinitions.GetTargetTypeName(type)} x{count}");
            }

            return success;
        }

        /// <summary>
        /// 清空所有收集目标
        /// </summary>
        public void ClearAllTargetItems()
        {
            if (_targetData == null)
            {
                Debug.LogError("目标数据为空，无法清空目标");
                return;
            }

            _targetData.ClearTargetItems();
            OnTargetItemsChanged?.Invoke(_targetData.targetItems);
            _editorManager?.SetDirty(true);
            Debug.Log("已清空所有收集目标");
        }

        /// <summary>
        /// 验证目标数据
        /// </summary>
        public bool ValidateTargetData()
        {
            if (_targetData == null)
            {
                Debug.LogError("目标数据为空");
                return false;
            }

            return _targetData.IsValid();
        }

        /// <summary>
        /// 获取目标摘要信息
        /// </summary>
        public string GetTargetSummary()
        {
            if (_targetData == null)
                return "无目标数据";

            return _targetData.GetSummary();
        }

        /// <summary>
        /// 重置为默认分数关
        /// </summary>
        public void ResetToScoreLevel(int defaultScore = 1000)
        {
            if (_targetData == null)
            {
                Debug.LogError("目标数据为空，无法重置");
                return;
            }

            _targetData.ResetToScoreLevel(defaultScore);
            OnTargetTypeChanged?.Invoke(LevelTargetType.Score);
            OnTargetScoreChanged?.Invoke(defaultScore);
            OnTargetItemsChanged?.Invoke(_targetData.targetItems);
            _editorManager?.SetDirty(true);
            Debug.Log($"已重置为分数关，目标分数: {defaultScore}");
        }

        /// <summary>
        /// 重置为默认收集关
        /// </summary>
        public void ResetToCollectionLevel()
        {
            if (_targetData == null)
            {
                Debug.LogError("目标数据为空，无法重置");
                return;
            }

            _targetData.ResetToCollectionLevel();
            OnTargetTypeChanged?.Invoke(LevelTargetType.Collection);
            OnTargetScoreChanged?.Invoke(0);
            OnTargetItemsChanged?.Invoke(_targetData.targetItems);
            _editorManager?.SetDirty(true);
            Debug.Log("已重置为收集关");
        }

        /// <summary>
        /// 获取可用的目标类型（排除已添加的）
        /// </summary>
        public int[] GetAvailableTargetTypes()
        {
            if (_targetData == null)
                return TargetTypeDefinitions.GetAllTargetTypes();

            return TargetTypeDefinitions.GetAllTargetTypes()
                .Where(type => !_targetData.targetItems.Any(item => item.type == type))
                .ToArray();
        }

        /// <summary>
        /// 获取目标统计信息
        /// </summary>
        public TargetStatistics GetTargetStatistics()
        {
            var stats = new TargetStatistics();
            
            if (_targetData == null)
                return stats;

            stats.targetType = _targetData.TargetType;
            stats.targetScore = _targetData.targetScore;
            stats.targetItemCount = _targetData.targetItems.Count;
            stats.isValid = _targetData.IsValid();

            return stats;
        }
    }

    /// <summary>
    /// 目标统计信息
    /// </summary>
    public class TargetStatistics
    {
        public LevelTargetType targetType;
        public int targetScore;
        public int targetItemCount;
        public bool isValid;

        public override string ToString()
        {
            if (targetType == LevelTargetType.Score)
            {
                return $"分数关: {targetScore}分, 有效: {isValid}";
            }
            else
            {
                return $"收集关: {targetItemCount}个目标, 有效: {isValid}";
            }
        }
    }
}

#endif
