#if UNITY_EDITOR

using System;
using System.Collections.Generic;
using UnityEngine;
using Newtonsoft.Json;

namespace BlockLevelEditor.Core.Data
{
    /// <summary>
    /// 瓦片数据模型 - 完全匹配游戏JSON格式
    /// 支持新的统一数据结构：baseType, color, gem, advancedBlockId, blockLayers, isFixed, overlays
    /// </summary>
    [System.Serializable]
    public class TileData
    {
        [JsonProperty("x")]
        public int x;

        [JsonProperty("y")]
        public int y;

        [JsonProperty("baseType")]
        public string baseType = "color"; // "color", "gem", "other"

        [JsonProperty("color")]
        public int color = 0;

        [JsonProperty("gem")]
        public int gem = 0;

        [JsonProperty("advancedBlockId")]
        public int advancedBlockId = 0;

        [JsonProperty("blockLayers")]
        public int blockLayers = 0;

        [JsonProperty("isFixed")]
        public bool isFixed = false;

        [JsonProperty("overlays")]
        public List<OverlayData> overlays = new List<OverlayData>();

        // 2x2方块相关属性（不序列化到JSON，仅编辑器内部使用）
        [JsonIgnore]
        public bool is2x2 = false;

        [JsonIgnore]
        public bool is2x2Master = false; // 是否为2x2方块的主方块（左上角）

        [JsonIgnore]
        public Vector2Int masterPosition = Vector2Int.zero; // 如果是从属方块，指向主方块位置

        [JsonIgnore]
        public Vector2Int[] occupiedPositions = null; // 2x2方块占用的所有位置（仅主方块使用）

        // 构造函数
        public TileData()
        {
        }

        public TileData(int x, int y)
        {
            this.x = x;
            this.y = y;
        }

        // 便捷属性 - 不序列化
        [JsonIgnore]
        public Vector2Int Position => new Vector2Int(x, y);

        [JsonIgnore]
        public bool IsEmpty => baseType == "color" && color == 0 && gem == 0 &&
                              advancedBlockId == 0 && blockLayers == 0 &&
                              !isFixed && (overlays == null || overlays.Count == 0);

        [JsonIgnore]
        public bool HasAdvancedBlock => advancedBlockId > 0;

        [JsonIgnore]
        public bool HasOverlays => overlays != null && overlays.Count > 0;

        [JsonIgnore]
        public bool IsColorBlock => baseType == "color" && color > 0;

        [JsonIgnore]
        public bool IsGemBlock => baseType == "gem" && gem > 0;

        // 数据验证
        public bool IsValid()
        {
            // 基本位置验证
            if (x < 0 || x >= 8 || y < 0 || y >= 8)
                return false;
            
            // baseType必须是有效值
            if (baseType != "color" && baseType != "gem" && baseType != "other")
                return false;
            
            // 颜色和宝石值验证
            if (color < 0 || gem < 0)
                return false;
            
            // 特殊方块验证
            if (advancedBlockId < 0 || blockLayers < 0)
                return false;
            
            // 覆盖物验证
            if (overlays != null)
            {
                foreach (var overlay in overlays)
                {
                    if (!overlay.IsValid())
                        return false;
                }
            }
            
            return true;
        }

        // 深拷贝
        public TileData Clone()
        {
            var clone = new TileData(x, y)
            {
                baseType = this.baseType,
                color = this.color,
                gem = this.gem,
                advancedBlockId = this.advancedBlockId,
                blockLayers = this.blockLayers,
                isFixed = this.isFixed,
                // 复制2x2方块相关属性
                is2x2 = this.is2x2,
                is2x2Master = this.is2x2Master,
                masterPosition = this.masterPosition
            };

            // 复制占用位置数组
            if (occupiedPositions != null)
            {
                clone.occupiedPositions = new Vector2Int[occupiedPositions.Length];
                Array.Copy(occupiedPositions, clone.occupiedPositions, occupiedPositions.Length);
            }

            if (overlays != null)
            {
                clone.overlays = new List<OverlayData>();
                foreach (var overlay in overlays)
                {
                    clone.overlays.Add(overlay.Clone());
                }
            }

            return clone;
        }

        // 清空数据
        public void Clear()
        {
            baseType = "color";
            color = 0;
            gem = 0;
            advancedBlockId = 0;
            blockLayers = 0;
            isFixed = false;
            overlays?.Clear();

            // 清空2x2方块相关属性
            is2x2 = false;
            is2x2Master = false;
            masterPosition = Vector2Int.zero;
            occupiedPositions = null;
        }

        // 设置颜色方块
        public void SetColorBlock(int colorValue)
        {
            Clear();
            baseType = "color";
            color = colorValue;
        }

        // 设置宝石方块
        public void SetGemBlock(int gemValue)
        {
            Clear();
            baseType = "gem";
            gem = gemValue;
        }

        // 设置特殊方块
        public void SetAdvancedBlock(int blockId, int layers = 1)
        {
            if (baseType == "color" && color == 0)
                baseType = "other";
            
            advancedBlockId = blockId;
            blockLayers = layers;
        }

        // 添加覆盖物（使用OverlayManager）
        public bool AddOverlay(int overlayId, int layers = 1)
        {
            var result = Manager.OverlayManager.AddOverlay(this, overlayId, layers);
            return result == Manager.OverlayManager.OverlayOperationResult.Success;
        }

        // 移除覆盖物（使用OverlayManager）
        public bool RemoveOverlay(int overlayId)
        {
            var result = Manager.OverlayManager.RemoveOverlay(this, overlayId);
            return result == Manager.OverlayManager.OverlayOperationResult.Success;
        }

        // 更新覆盖物层数
        public bool UpdateOverlayLayers(int overlayId, int newLayers)
        {
            var result = Manager.OverlayManager.UpdateOverlayLayers(this, overlayId, newLayers);
            return result == Manager.OverlayManager.OverlayOperationResult.Success;
        }

        // 清空所有覆盖物
        public void ClearAllOverlays()
        {
            Manager.OverlayManager.ClearAllOverlays(this);
        }

        // 获取覆盖物信息列表
        public List<Manager.OverlayInfo> GetOverlayInfos()
        {
            return Manager.OverlayManager.GetOverlayInfos(this);
        }

        /// <summary>
        /// 设置为2x2方块
        /// </summary>
        public void SetAs2x2Block(int blockId, int layers, bool isMaster, Vector2Int masterPos = default)
        {
            baseType = "other";
            advancedBlockId = blockId;
            blockLayers = layers;
            is2x2 = true;
            is2x2Master = isMaster;

            if (isMaster)
            {
                masterPosition = new Vector2Int(x, y);
                // 计算占用的4个位置
                occupiedPositions = new Vector2Int[]
                {
                    new Vector2Int(x, y),       // 左上
                    new Vector2Int(x + 1, y),   // 右上
                    new Vector2Int(x, y + 1),   // 左下
                    new Vector2Int(x + 1, y + 1) // 右下
                };
            }
            else
            {
                masterPosition = masterPos;
                occupiedPositions = null;
            }
        }

        /// <summary>
        /// 检查是否为2x2方块的一部分
        /// </summary>
        public bool Is2x2Part => is2x2;

        /// <summary>
        /// 检查是否为2x2方块的主方块
        /// </summary>
        public bool Is2x2Master => is2x2 && is2x2Master;

        /// <summary>
        /// 检查是否为2x2方块的从属方块
        /// </summary>
        public bool Is2x2Slave => is2x2 && !is2x2Master;

        /// <summary>
        /// 获取2x2方块占用的所有位置
        /// </summary>
        public Vector2Int[] GetOccupiedPositions()
        {
            if (Is2x2Master && occupiedPositions != null)
                return occupiedPositions;

            return new Vector2Int[] { new Vector2Int(x, y) };
        }

        // 验证覆盖物配置
        public bool ValidateOverlays(out List<string> errors)
        {
            return Manager.OverlayManager.ValidateOverlays(this, out errors);
        }

        public override string ToString()
        {
            return $"Tile({x},{y}): {baseType}, color={color}, gem={gem}, advanced={advancedBlockId}, layers={blockLayers}, fixed={isFixed}, overlays={overlays?.Count ?? 0}";
        }
    }

    /// <summary>
    /// 覆盖物数据
    /// </summary>
    [System.Serializable]
    public class OverlayData
    {
        [JsonProperty("id")]
        public int id;
        
        [JsonProperty("layers")]
        public int layers = 1;

        public bool IsValid()
        {
            return id > 0 && layers > 0;
        }

        public OverlayData Clone()
        {
            return new OverlayData { id = this.id, layers = this.layers };
        }

        public override string ToString()
        {
            return $"Overlay(id={id}, layers={layers})";
        }
    }
}

#endif
