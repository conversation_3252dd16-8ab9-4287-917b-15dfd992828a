#if UNITY_EDITOR

using System.Collections.Generic;
using System.IO;
using System.Text;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;

namespace BlockLevelEditor.Tests
{
    /// <summary>
    /// 搜索包含指定目标ID的关卡配置文件窗口
    /// </summary>
    public class LevelTargetSearchWindow : EditorWindow
    {
        private string inputTargetId = string.Empty;
        private readonly List<string> matchedFiles = new List<string>();
        private Vector2 scrollPos;

        public static void ShowWindow()
        {
            var window = GetWindow<LevelTargetSearchWindow>(true, "关卡目标文件搜索", true);
            window.minSize = new Vector2(520, 420);
            window.Show();
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("输入目标ID并搜索包含该ID的关卡配置文件（兼容 targets 数组与 targets.targetItems）", EditorStyles.wordWrappedLabel);
            EditorGUILayout.Space(6);

            EditorGUILayout.BeginHorizontal();
            inputTargetId = EditorGUILayout.TextField("目标ID:", inputTargetId);
            using (new EditorGUI.DisabledScope(string.IsNullOrEmpty(inputTargetId)))
            {
                if (GUILayout.Button("搜索", GUILayout.Width(100)))
                {
                    DoSearch();
                }
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(8);
            EditorGUILayout.LabelField($"结果数量: {matchedFiles.Count}");

            EditorGUILayout.Space(4);
            EditorGUILayout.BeginVertical("box");
            scrollPos = EditorGUILayout.BeginScrollView(scrollPos);
            foreach (var fileName in matchedFiles)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField(fileName);
                if (GUILayout.Button("复制文件名", GUILayout.Width(100)))
                {
                    EditorGUIUtility.systemCopyBuffer = fileName;
                }
                if (GUILayout.Button("定位资产", GUILayout.Width(80)))
                {
                    string assetPath = Path.Combine("Assets/Res/LevelLayout", fileName).Replace('\\', '/');
                    var asset = AssetDatabase.LoadAssetAtPath<TextAsset>(assetPath);
                    if (asset != null)
                    {
                        EditorGUIUtility.PingObject(asset);
                        Selection.activeObject = asset;
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("提示", $"未找到资产: {assetPath}", "确定");
                    }
                }
                EditorGUILayout.EndHorizontal();
            }
            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical();
        }

        private void DoSearch()
        {
            matchedFiles.Clear();
            if (!int.TryParse(inputTargetId, out int targetId))
            {
                EditorUtility.DisplayDialog("输入错误", "请输入有效的整数目标ID", "确定");
                return;
            }

            string folderPath = Path.Combine(Application.dataPath, "Res/LevelLayout");
            if (!Directory.Exists(folderPath))
            {
                EditorUtility.DisplayDialog("目录不存在", folderPath, "确定");
                return;
            }

            string[] jsonFiles = Directory.GetFiles(folderPath, "*.json", SearchOption.TopDirectoryOnly);
            int scanned = 0;
            int errors = 0;
            StringBuilder errorSb = new StringBuilder();

            foreach (var file in jsonFiles)
            {
                scanned++;
                string fileName = Path.GetFileName(file);
                try
                {
                    string content = File.ReadAllText(file);
                    var token = JToken.Parse(content);
                    if (token.Type != JTokenType.Object)
                    {
                        continue;
                    }

                    var obj = (JObject)token;
                    var targetsToken = obj["targets"];
                    if (targetsToken == null)
                    {
                        continue;
                    }

                    // 统一提取为数组（兼容两种结构）
                    JArray targetItemsArray = null;
                    if (targetsToken.Type == JTokenType.Array)
                    {
                        targetItemsArray = (JArray)targetsToken;
                    }
                    else if (targetsToken.Type == JTokenType.Object)
                    {
                        var ti = ((JObject)targetsToken)["targetItems"];
                        if (ti != null && ti.Type == JTokenType.Array)
                        {
                            targetItemsArray = (JArray)ti;
                        }
                    }

                    if (targetItemsArray == null)
                    {
                        continue;
                    }

                    foreach (var t in targetItemsArray)
                    {
                        if (t.Type != JTokenType.Object) continue;
                        var to = (JObject)t;
                        var typeProp = to["type"];
                        if (typeProp == null) continue;

                        int value;
                        switch (typeProp.Type)
                        {
                            case JTokenType.Integer:
                                value = typeProp.Value<int>();
                                break;
                            case JTokenType.Float:
                                value = Mathf.RoundToInt(typeProp.Value<float>());
                                break;
                            case JTokenType.String:
                                if (!int.TryParse(typeProp.Value<string>(), out value)) continue;
                                break;
                            default:
                                continue;
                        }

                        if (value == targetId)
                        {
                            matchedFiles.Add(fileName);
                            break; // 已匹配，检查下一个文件
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    errors++;
                    errorSb.AppendLine($"{fileName}: {ex.Message}");
                }
            }

            Repaint();

            string summary = $"扫描文件: {scanned}，匹配: {matchedFiles.Count}，错误: {errors}";
            if (errors > 0)
            {
                Debug.LogError($"目标ID搜索完成（存在错误）: {summary}\n{errorSb}");
            }
            else
            {
                Debug.Log($"目标ID搜索完成: {summary}");
            }
        }
    }
}

#endif


