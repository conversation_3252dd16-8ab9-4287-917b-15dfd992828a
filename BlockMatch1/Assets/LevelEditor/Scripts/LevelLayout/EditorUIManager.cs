
#if UNITY_EDITOR

using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;


namespace LevelEditor.Scripts.LevelLayout
{
    public class EditorUIManager: MonoBehaviour
    {

        public Canvas UIRoot;

        public static EditorUIManager Instance;
       

        private Dictionary<string, LevelEditor_ViewBase> editorViewMap = new Dictionary<string, LevelEditor_ViewBase>();

        private void Awake()
        {
            Instance = this;
        }


        public T ShowUI<T>() where T : LevelEditor_ViewBase
        {
            string name = typeof(T).Name;
            if (!editorViewMap.TryGetValue(name, out var editorView))
            {
                var obj =(GameObject)AssetDatabase.LoadAssetAtPath($"Assets/LevelEditor/Prefabs/UI/{name}.prefab",typeof(GameObject));
                var ui = GameObject.Instantiate(obj, UIRoot.transform);
                editorView = ui.GetComponent<T>();
                editorViewMap[name] = editorView;
            }
            editorView.OnShow();
            return editorView as T;
        }

        public void HideUI<T>()where T : LevelEditor_ViewBase
        {
            string name = typeof(T).Name;
            if (editorViewMap.TryGetValue(name, out var editorView))
            {
                editorView.OnHide();
            }
        }

        public void HideAllUI()
        {
            foreach (var editorView in editorViewMap.Values)
            {
                editorView.OnHide();
            }
        }
    }
}
#endif