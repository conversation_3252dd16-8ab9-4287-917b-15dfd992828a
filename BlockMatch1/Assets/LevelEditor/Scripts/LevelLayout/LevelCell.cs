#if UNITY_EDITOR

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System;

namespace LevelEditor.Scripts.LevelLayout
{
    public class LevelCell : MonoBehaviour
    {
        public Button RemoveBtn;
        public Button Btn;
        public TextMeshProUGUI Txt;

        public string LevelID;

        private Action<string,int> OnClickHandler;

        private void Awake()
        {
            Btn.onClick.AddListener(OnClick);
            RemoveBtn.onClick.AddListener(OnClickRemvoe);
        }

        private void OnClickRemvoe()
        {
            OnClickHandler?.Invoke(LevelID,-1);
        }

        private void OnClick()
        {
            OnClickHandler?.Invoke(LevelID,0);
        }

        public void Initialize(string levelID, Action<string,int> onLevelSelected)
        {
            LevelID = levelID;
            Txt.text = levelID.ToString();
            OnClickHandler = onLevelSelected;
        }
    }
}
#endif