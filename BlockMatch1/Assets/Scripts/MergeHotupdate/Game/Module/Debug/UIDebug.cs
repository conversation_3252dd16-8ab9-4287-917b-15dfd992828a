#if DEBUG || DEVELOPMENT_BUILD

// **********************************************
// Copyright(c) 2021 by com.ustar
// All right reserved
// 
// Author : <PERSON><PERSON><PERSON>
// Date : 2023/07/12/17:25
// Ver : 1.0.0
// Description : UIDebug.cs
// ChangeLog :
// **********************************************

using System;
using System.Collections.Generic;
using DragonPlus.Core;
using Framework;
using SRDebugger.Internal;
using UnityEngine;
using UnityEngine.UI;
using TMGame;

public class UIDebug : UIViewBase
{
    private Button closeButton;
    private Transform menuContent;
    private Transform menuItem;
    private Transform debugContent;
    private Transform debugItem;
    private InputField inputField1;
    private InputField inputField2;

    private Dictionary<string, List<OptionDefinition>> _options;
    private GameObject selectObj;
    private OptionDefinition inputOption1;
    private OptionDefinition inputOption2;

    protected override void OnCreate()
    {
        base.OnCreate();
        GetConfig();
        AddMenuButton();
        AddInputFiledListener();
    }


    protected override void BindComponent()
    {
        base.BindComponent();
        closeButton = GO.transform.Find("Root/CloseButton").GetComponent<Button>();
        menuContent = GO.transform.Find("Root/MenuGroup/MenuScrollView/Viewport/Content");
        menuItem = GO.transform.Find("Root/MenuGroup/MenuScrollView/Viewport/Content/MenuButton");
        debugContent = GO.transform.Find("Root/InsideGroup/ScrollView/Viewport/Content");
        debugItem = GO.transform.Find("Root/InsideGroup/ScrollView/Viewport/Content/DebugButton");
        inputField1 = GO.transform.Find("Root/InputField1").GetComponent<InputField>();
        inputField2 = GO.transform.Find("Root/InputField2").GetComponent<InputField>();
    }

    protected override void RegisterGameEvent()
    {
        base.RegisterGameEvent();
        closeButton.onClick.AddListener(() => { Close(); });
    }

    protected override void RemoveGameEvent()
    {
        base.RemoveGameEvent();
        closeButton.onClick.RemoveListener(() => { Close(); });
    }

    private void AddInputFiledListener()
    {
        inputField1.onValueChanged.AddListener((param) => { OnChangeInputField1(); });
        inputField2.onValueChanged.AddListener((param) => { OnChangeInputField2(); });
    }

    private void OnChangeInputField1()
    {
        var inputText = inputField1.text;
        if (!string.IsNullOrEmpty(inputText))
        {
            if (inputOption1 != null)
            {
                inputOption1.Property.SetValue(Convert.ChangeType(inputText, inputOption1.Property.PropertyType));
            }
        }
    }

    private void OnChangeInputField2()
    {
        var inputText = inputField2.text;
        if (!string.IsNullOrEmpty(inputText))
        {
            if (inputOption2 != null)
            {
                inputOption2.Property.SetValue(Convert.ChangeType(inputText, inputOption2.Property.PropertyType));
            }
        }
    }

    private void AddMenuButton()
    {
        menuItem.gameObject.SetActive(false);
        debugItem.gameObject.SetActive(false);
        foreach (var kv in _options)
        {
            GameObject obj = GameObject.Instantiate(menuItem.gameObject, menuContent);
            obj.gameObject.SetActive(true);
            obj.FindChild("Text").GetComponent<Text>().text = kv.Key;
            obj.FindChild("Selected").gameObject.SetActive(false);
            var name = kv.Key;
            obj.GetComponent<Button>().onClick.AddListener(() => { RefreshDebugButtonShow(name, obj); });
            // 默认选中活动
            if (name.Equals("活动"))
            {
                RefreshDebugButtonShow(name, obj);
            }
        }
    }


    private void RefreshDebugButtonShow(string menuString, GameObject newObj)
    {
        List<OptionDefinition> list;
        var hasValue = _options.TryGetValue(menuString, out list);
        if (hasValue)
        {
            if (selectObj != null)
            {
                selectObj.FindChild("Selected").gameObject.SetActive(false);
            }

            newObj.FindChild("Selected").gameObject.SetActive(true);
            selectObj = newObj;
            for (var i = 0; i < debugContent.childCount; i++)
            {
                var child = debugContent.GetChild(i);
                if (child.gameObject.name != "DebugButton")
                {
                    GameObject.Destroy(child.gameObject);
                }
            }

            inputField1.text = "";
            inputField2.text = "";

            inputOption1 = null;
            inputOption2 = null;
            foreach (var optionInfo in list)
            {
                var method = optionInfo.Method;

                if (method != null)
                {
                    GameObject obj = GameObject.Instantiate(debugItem.gameObject, debugContent);
                    obj.gameObject.SetActive(true);
                    obj.FindChild("Text").GetComponent<Text>().text = optionInfo.Name;
                    obj.GetComponent<Button>().onClick.AddListener(() => { method.Invoke(null); });
                }
                else
                {
                    if (inputOption1 == null)
                    {
                        inputOption1 = optionInfo;
                    }
                    else
                    {
                        inputOption2 = optionInfo;
                    }
                }
            }
        }
    }

    private void GetConfig()
    {
        if (_options != null)
        {
            return;
        }


        DebugOption.Init();

        _options = new Dictionary<string, List<OptionDefinition>>();

        foreach (var option in Service.Options.Options)
        {
            List<OptionDefinition> list;

            if (!_options.TryGetValue(option.Category, out list))
            {
                list = new List<OptionDefinition>();
                _options[option.Category] = list;
            }

            list.Add(option);
        }

        foreach (var kv in _options)
        {
            kv.Value.Sort((d1, d2) => d1.SortPriority.CompareTo(d2.SortPriority));
        }
    }
}

#endif