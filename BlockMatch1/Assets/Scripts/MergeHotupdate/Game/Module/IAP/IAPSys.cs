using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using DragonPlus;
using DragonPlus.Config;
using DragonPlus.Config.Global;
using DragonPlus.ConfigHub.Ad;
using DragonPlus.Core;
using DragonPlus.InAppPurchasing;
using DragonPlus.Network;
using DragonPlus.Save;
using DragonU3DSDK.Network.API.Protocol;
using Framework;
using Newtonsoft.Json;
using TMGame.Storage;
using UnityEngine;
using UnityEngine.Purchasing;

namespace TMGame
{
    public enum ShopType
    {
        Coin = 1,
        Pack = 2,
        NoAd = 3,
        IceBreaking = 8,

        Recharge = 6, // 累充礼包
        GoldenPass = 7, // 战令(Pass)
        TripleGift = 11,
        MergeShop = 12,
        MergeBalloon = 13,
    }

    public class IAPSys : LogicSys
    {
        private IAPModel model;

        private bool inPaying;

        public bool inDebugPayMode;
        private object m_UserData;
        
        // private ClientGroupModel _clientGroupModel;
        private Dictionary<ShopType, PaymentHandler> _handlerNameDict;

        private CancellationTokenSource tokenSource;

        private readonly List<ShopType> NeedShowPopTypes = new List<ShopType>() { ShopType.Pack, ShopType.Coin, ShopType.NoAd };
        public override void Init()
        {
            base.Init();
            model = new IAPModel(this);
            // _clientGroupModel = new ClientGroupModel(this);
            InitPaymentHandleDic();
            InitShopConfig();
        }

        // public ClientGroupModel GetClientGroupModel()
        // {
        //     return _clientGroupModel;
        // }
        
        public override void OnShutDown()
        {
            if (tokenSource != null)
            {
                tokenSource.Cancel();
                tokenSource = null;
            }

            inPaying = false;
            _handlerNameDict.Clear();
            _handlerNameDict = null;
            model = null;
            base.OnShutDown();
        }

        private void InitShopConfig()
        {
            InitIAP();
        }

        public override void SubscribeEvents()
        {
            base.SubscribeEvents();
            EventBus.Subscribe<UnfulfilledPaymentPending>(OnFindPaddingPayment);
        }

        private void OnFindPaddingPayment(UnfulfilledPaymentPending evt)
        {
            var productId = evt.data.ProductId;
            if (productId.Equals(""))
            {
                Debug.LogError("补单：----OnFindPaddingPayment-------- [message.data.ProductId] empty!");
                return;
            }

            if (evt.data.pending == false)
            {
                Debug.LogError("补单：----OnFindPaddingPayment-------- 收到补单通知，订单状态为 pending！不用处理！");
                return;
            }
            // 尝试补单
            Debug.Log($"补单:----OnFindPaddingPayment---- 收到补单通知，尝试补单!");
            //if (!TMGame.GameGlobal.GetMod<GuideSys>().IsShowingGuide())
            {
                TryHandleUnfulfilledPayments();
            }
        }

        // public List<DragonPlus.ConfigHub.IAP.Mapping> GetMappingList()
        // {
        //     return IAPConfigManager.Instance.GetConfig<DragonPlus.ConfigHub.IAP.Mapping>();
        // }

        public int GetCurGroup()
        {
            var groups = GetAdMappingList();
            if (groups != null && groups.Count > 0)
            {
                var group = groups[0].UserGroup;
                return group;
            }
            return 0;
        }
        

        public List<DragonPlus.ConfigHub.Ad.Mapping> GetAdMappingList()
        {
            return AdConfigManager.Instance.GetConfig<DragonPlus.ConfigHub.Ad.Mapping>();
        }

        public List<ItemPack> GetItemPackList()
        {
            return AdConfigManager.Instance.GetConfig<ItemPack>();
        }

        public List<RemoveAd> GetRemoveAdList()
        {
            return AdConfigManager.Instance.GetConfig<RemoveAd>();
        }

        public List<AdReward> GetAdRewardList()
        {
            return AdConfigManager.Instance.GetConfig<AdReward>();
        }
        public List<AdTask> GetAdTaskList()
        {
            return AdConfigManager.Instance.GetConfig<AdTask>();
        }

        public List<ChestContent> GetChestContent()
        {
            return AdConfigManager.Instance.GetConfig<ChestContent>();
        }

        public List<Table_Global_Shop> GetIAPItemDataList()
        {
            return model.GetIAPItemData();
        }

        public IAPModel GetModal()
        {
            return model;
        }

        #region ShopConfig

        public List<Table_Global_Shop> GetTableShop()
        {
            return GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_Global_Shop>();
        }
        public Table_Global_Shop GetTableShopByID(int id)
        {
            return GameGlobal.GetMgr<ConfigMgr>().GetConfig<Table_Global_Shop>(id);
        }

        public Table_Global_Shop GetTableShopByProductId(string purchaseId)
        {
            return GetTableShop().Find((sc) =>
            {
#if UNITY_IOS
            return purchaseId == sc.Product_id_ios;
#else
                return purchaseId == sc.Product_id;
#endif
            });
        }

        public RemoveAd GetRemoveAdCfg()
        {
            return model.GetRemoveAdCfg();
        }

        #endregion

        private void InitIAP()
        {
            var configs = GetTableShop();
            var consumableProductIds = new List<string>();
            var nonconsumableProductIds = new List<string>();
            var subProductIds = new List<string>();
            foreach (var config in configs)
            {
                var productId = "";
#if UNITY_IOS
            productId = config.Product_id_ios;
#elif UNITY_ANDROID
                productId = config.Product_id;
#endif

                switch (config.PurchaseType)
                {
                    case 0:
                        consumableProductIds.Add(productId);
                        break;

                    case 1:
                        nonconsumableProductIds.Add(productId);
                        break;

                    case 2:
                        subProductIds.Add(productId);
                        break;
                }
            }
#if !UNITY_STANDALONE
            SDK<IAP>.Instance.Init(consumableProductIds, nonconsumableProductIds, subProductIds);
#endif

            RequestUnfulfilledPaymentsAndTryVerify();
        }

        public Table_Global_Shop GetShopCfgByProductId(string productId)
        {
            var shopList = GetTableShop();
#if UNITY_IOS
            return shopList.Find(x => x.Product_id_ios == productId);
#else
            return shopList.Find(x => x.Product_id == productId);
#endif
        }

        private List<T> LoadTable<T>(string tableAddress)
        {
            var path = $"{tableAddress}";

            var ta = GameGlobal.GetMgr<ResMgr>().GetRes<TextAsset>(path).GetInstance(GameGlobal.DontDestoryRoot);

            if (ta == null || string.IsNullOrEmpty(ta.text))
            {
                Log.Error($"Load {path} error!");
                return new List<T>();
            }

            return JsonConvert.DeserializeObject<List<T>>(ta.text);
        }

        private void InitPaymentHandleDic()
        {
            _handlerNameDict = new Dictionary<ShopType, PaymentHandler>();
            _handlerNameDict.Add(ShopType.Coin, new StorePaymentHandler());
        }

        public void Purchase(int purchaseId, object userData)
        {
            if (tokenSource != null)
            {
                tokenSource.Cancel();
            }

            m_UserData = userData;

            var shopConfig = GetTableShopByID(purchaseId);

            // var currentType = BIHelper.GetCurEnvForBI();
            // BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventIapShopId,
            //     data1: shopConfig.Id.ToString(),
            //     data2: shopConfig.Price, data3: currentType);

            string productId = "";
#if UNITY_IOS
        productId = shopConfig.Product_id_ios;
#elif UNITY_ANDROID
            productId = shopConfig.Product_id;
#endif

            var products = SDK<IAP>.Instance.GetAllProductInfo();
#if !PRODUCTION_PACKAGE
            if (Application.isEditor || inDebugPayMode)
            {
                // 模拟真机支付
                GameGlobal.GetMgr<UIMgr>().ShowWaiting();
                TMUtility.WaitSeconds(2, () =>
                {
                    GameGlobal.GetMgr<UIMgr>().CloseWaiting();
                    if (_handlerNameDict.ContainsKey(shopConfig.GetIAPShopType()))
                    {
                        _handlerNameDict[shopConfig.GetIAPShopType()].HandlePaymentSuccess();
                    }

                    GetIAPReward(shopConfig);
                }).Forget();
                return;
            }
#endif

            if (products == null || products.Length <= 0 ||
                Application.internetReachability == NetworkReachability.NotReachable)
            {
                UIView_Notice.ViewData viewData = new UIView_Notice.ViewData()
                {
#if UNITY_ANDROID
                    content = LocalizationManager.Instance.GetLocalizedString("&key.UI_cannot_connect_to_google_play"),
#elif UNITY_IOS
                content = LocalizationManager.Instance.GetLocalizedString("&key.UI_cannot_connect_to_itunes_store"),
#else
                content = LocalizationManager.Instance.GetLocalizedString("&key.UI_purchase_failed"),
#endif
                    showCloseBtn = true,
                    showMidBtn = true,
                };
                GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, viewData);
                return;
            }

            if (purchaseId <= 0)
            {
                return;
            }

            Log.Info($"Purchase: productId{productId}");

            // foreach (var p in products)
            // {
            //     Log.Info(" 商品 : " + p.definition.id + "  ,hasReceipt :" + p.hasReceipt + "  ,receipt: " +
            //                   p.receipt + " , transactionID : " + p.transactionID);
            // }

            GameGlobal.GetMgr<UIMgr>().ShowWaiting();
            inPaying = true;
#if UNITY_ANDROID
            if (SDK<IAP>.Instance.IsProductAlreadyOwned(productId))
            {
                // 该商品有未完成订单，尝试一次补单
                Log.Warning($"Purchase: productId{productId} 该商品为未完成状态，尝试补单，取消本次付费行为!");
                RequestUnfulfilledPaymentsAndTryVerify(productId);
                return;
            }
#endif

            SDK<IAP>.Instance.PurchaseProduct(productId, OnPurchased);

            Log.Info($"Purchase: purchaseId{purchaseId} end");
            tokenSource = new CancellationTokenSource();
            TMUtility.WaitSeconds(120, () =>
            {
                tokenSource = null;
                if (inPaying)
                {
                    GameGlobal.GetMgr<UIMgr>().CloseWaiting();
                    EventBus.Dispatch(new IAPFailure(shopConfig));
                    // TODO 发送Bi日志
                }
            }, tokenSource.Token).Forget();
        }

        public void TryHandleUnfulfilledPayments()
        {
            Log.Info($"补单: TryHandleUnfulfilledPayments 尝试补单");
            string productId = string.Empty;
            Product[] products = SDK<IAP>.Instance.GetAllProductInfo();
            if (products == null || products.Length <= 0) return;
            for (int i = 0; i < products.Length; i++)
            {
                var id = products[i].definition.id;
                Log.Info($"补单: 商店ID：{id}");
                if (SDK<IAP>.Instance.IsProductAlreadyOwned(id))
                {
                    productId = id;
                    break;
                }
            }
            if (string.IsNullOrEmpty(productId))
            {
                CLog.Info($"补单: TryHandleUnfulfilledPayments:无需补单");
                return;
            }
            CLog.Info($"补单: TryHandleUnfulfilledPayments 确认补单:{productId}");
            RequestUnfulfilledPaymentsAndTryVerify(productId);
        }

        public void RequestUnfulfilledPaymentsAndTryVerify(string checkProductId = "")
        {
            SDK<INetwork>.Instance.Send(new CListUnfulfilledPayments(),
                (SListUnfulfilledPayments obj) =>
                {
                    var payments = obj;
                    Log.Info($"补单: CListUnfulfilledPayments success !--{payments.Payments.Count}");
                    foreach (var payment in payments.Payments)
                    {
                        SDK<IAP>.Instance.SetUnfulfilledPaymentId(payment.ProductId, payment.PaymentId);
                    }

                    SDK<IAP>.Instance.VerifyUnfulfilledPayment(OnPurchased, checkProductId);
                },
                (arg1, arg2, arg3) => { Log.Info("补单: CListUnfulfilledPayments error : " + arg1 + "  " + arg2 + "  " + arg3); });
        }

        void OnPurchased(bool success, string productId, Product product, PurchaseFailureReason failureReason)
        {
            if (tokenSource != null)
            {
                tokenSource.Cancel();
            }

            GameGlobal.GetMgr<UIMgr>().CloseWaiting();

            Log.Info($"OnPurchased: productId{productId} >>>");

            var shopConfig = GetTableShopByProductId(productId);

            if (shopConfig == null)
            {
                Log.Warning("OnPurchased: shop Config not found in OnPurchased");
                return;
            }

            if (success && product != null)
            {
                Log.Info($"OnPurchased: success productId{productId} shopConfig.Id{shopConfig.Id} shopConfig.ShopType{shopConfig.ShopType}");

                if (_handlerNameDict.ContainsKey(shopConfig.GetIAPShopType()))
                {
                    _handlerNameDict[shopConfig.GetIAPShopType()].HandlePaymentSuccess();
                }

                GetIAPReward(shopConfig);
            }
            else
            {
                if (_handlerNameDict.ContainsKey(shopConfig.GetIAPShopType()))
                {
                    _handlerNameDict[shopConfig.GetIAPShopType()].HandlePaymentFailed();
                }
                EventBus.Dispatch(new IAPFailure(shopConfig));

                Log.Warning("OnPurchased failed with reason = " + failureReason.ToString());

                if (failureReason == PurchaseFailureReason.UserCancelled)
                {
                    UIView_Notice.ViewData viewData = new UIView_Notice.ViewData()
                    {
                        showMidBtn = true,
                        content = LocalizationManager.Instance.GetLocalizedString("&key.UI_common_iap"),
                    };
                    GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, viewData);
                }
                else
                {
                    UIView_Notice.ViewData viewData = new UIView_Notice.ViewData()
                    {
                        showMidBtn = true,
                        content = LocalizationManager.Instance.GetLocalizedString("&key.UI_purchase_failed_title"),
                    };
                    GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, viewData);
                }
            }

            inPaying = false;
        }

        private string GetProductId(Table_Global_Shop tableGameShopConfig)
        {
#if UNITY_IOS
        return tableGameShopConfig.Product_id_ios;
#else
            return tableGameShopConfig.Product_id;
#endif
        }

        private void GetIAPReward(Table_Global_Shop tableGameShopConfig)
        {
            string productId = GetProductId(tableGameShopConfig);
            Log.Info($"OnPurchased: success productId{productId} shopConfig.Id{tableGameShopConfig.Id} shopConfig.ShopType{tableGameShopConfig.ShopType}");

            model.AddPurchasedTimes(tableGameShopConfig.Id, 1);
            model.UpdatePlayerPayLastTime();

            var currentType = BIHelper.GetCurEnvForBI();
            BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventIapShopId,
                data1: tableGameShopConfig.Id.ToString(),
                data2: tableGameShopConfig.Price, data3: currentType);
          
            if (tableGameShopConfig.ItemId != null && tableGameShopConfig.GetIAPShopType() != ShopType.MergeShop)
            {
                List<DragonPlus.Config.Global.Table_Global_Item> items = new List<DragonPlus.Config.Global.Table_Global_Item>();
                for (int i = 0; i < tableGameShopConfig.ItemId.Count; i++)
                {
                    var item = TMItemUtility.GenerateDummyItem(tableGameShopConfig.ItemId[i],
                        tableGameShopConfig.ItemCnt[i]);

                    items.Add(item);
                }
                TMGame.GameGlobal.GetMod<UserProfileSys>().SettleRewards(items, new BIHelper.ItemChangeReasonArgs(BiEventBlockMatch1.Types.ItemChangeReason.Iap)
                {
                    data1 = productId
                });
            }

            if (tableGameShopConfig.GetIAPShopType() == ShopType.NoAd)
            {
                TMGame.GameGlobal.GetMod<AdSys>().SetRemoveAd();
            }

            if (tableGameShopConfig.GetIAPShopType() == ShopType.MergeShop)
            {
                var shopView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_ShopMain);
                if (shopView == null)
                {
                    if (tableGameShopConfig.ItemId != null && tableGameShopConfig.ItemId.Count > 0)
                    {
                        Log.Info("补单:宝石补单！");
                        //MergeWorld.Instance.TryAddUnfulfilledPaymentsElement(tableGameShopConfig.ItemId[0]);
                    }
                }
            }

            if (NeedShowPopTypes.Contains(tableGameShopConfig.GetIAPShopType()))
            {
                // if (GameGlobal.GetMod<GuideSys>().IsShowingGuide())
                // {
                //     RefreshResData(tableGameShopConfig);
                // }
                // else
                // {
                //     var iapSuccessPopupParams = new IapSuccessPopupParams();
                //     iapSuccessPopupParams.iapSuccess = new IAPSuccess(tableGameShopConfig, null);
                //     Game.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_ShopPurchase, iapSuccessPopupParams);
                // }
            }
            RefreshConfigHub();
            //  _clientGroupModel.PayUpdateClientGroup(tableGameShopConfig.Price);
            EventBus.Dispatch(new IAPSuccess(tableGameShopConfig, m_UserData));
        }

        private void RefreshResData(Table_Global_Shop tableGameShopConfig)
        {
            var shopConfig = tableGameShopConfig;
            var items = new List<Table_Global_Item>();

            if (shopConfig != null && shopConfig.ItemId != null && shopConfig.ItemId.Count > 0)
            {
                for (int i = 0; i < shopConfig.ItemId.Count; i++)
                {
                    if (shopConfig.ItemCnt[i] > 0)
                    {
                        var item = TMItemUtility.GenerateDummyItem(shopConfig.ItemId[i], shopConfig.ItemCnt[i]);
                        if (shopConfig.ItemCnt[i] > 0)
                        {
                            items.Add(item);
                        }
                    }
                }
            }
            foreach (var item in items)
            {
                EventBus.Dispatch(new EventCurrencyChange(item.GetItemType(), false, item.Amount));
            }
        }

        private void RefreshConfigHub()
        {
            try
            {
                //强制刷新一次玩家用户分组
                TMUtility.WaitSeconds(15, () => { SDK<ConfigHub>.Instance.FetchRemoteConfig(true); }).Forget();
            }
            catch (Exception e)
            {
                Log.Error(e.Message);
            }
        }

        /// <summary>
        /// 获得价格
        /// </summary>
        public string GetPrice(int shopId)
        {
            var shopConfig = GetTableShopByID(shopId);
            if (shopConfig==null)
            {
                return "";
            }
            var products = SDK<IAP>.Instance.GetAllProductInfo();
            if (products != null && products.Length > 0)
            {
                var product = GetProductInfo(shopConfig.Id);
                Log.Info("价格从平台获取" + product.metadata.localizedPriceString);

                return product.metadata.localizedPriceString;
            }
            Log.Info("价格从配置获取" + shopConfig.Price);
            return "$" + shopConfig.Price;
        }

        public Product GetProductInfo(int shopItemId)
        {
            var products = SDK<IAP>.Instance.GetAllProductInfo();
            if (products == null)
            {
                return null;
            }

            var shopConfig = GetTableShopByID(shopItemId);
            if (shopConfig == null)
            {
                return null;
            }

            foreach (var p in products)
            {
#if UNITY_IOS
            if (p.definition.storeSpecificId == shopConfig.Product_id_ios)
            {
                return p;
            }
#elif UNITY_ANDROID
                if (p.definition.storeSpecificId == shopConfig.Product_id)
                {
                    return p;
                }
#endif
            }

            return null;
        }

        public bool IsPlayerPurchased()
        {
            return model.IsPlayerPurchased();
        }

     
    }
}