using System;
using System.Collections.Generic;
using System.Threading;
using DragonPlus.Core;
using DragonPlus.Network;
using DragonU3DSDK.Network.API.Protocol;
using Framework;
using UnityEngine;
using YooAsset;

namespace TMGame
{
    public abstract class ActivityBase
    {
        protected bool downloadStatus = false;

        protected string activityType;

        protected string activityId;

        protected long StartTime { get; set; }

        protected long EndTime { get; set; }

        protected long RewardEndTime { get; set; }

        protected bool ManualEnd { get; set; }

        protected string StorageKey => "Activity_" + activityId;

        private CancellationTokenSource _cancellation;

        private bool _initedFromServer;

        protected ActivitySys _activitySys;

        public void OnCreate(string inActivityType, Activity serverActivity, ActivitySys inActivitySys)
        {
            activityType = inActivityType;
            activityId = serverActivity.ActivityId;
            _activitySys = inActivitySys;

            StartTime = (long) serverActivity.StartTime;
            EndTime = (long) serverActivity.EndTime;
            RewardEndTime = (long) serverActivity.RewardEndTime;
            ManualEnd = serverActivity.ManualEnd;

            _initedFromServer = true;

            InitConfig(serverActivity.Config);

            SubscribeEvents();
            SetExpireTimeCallback();
            SetCollectingTimeCallback();

            EventBus.Dispatch(new EventActivityCreate(activityType));

#if UNITY_EDITOR
            downloadStatus = true;
            EventBus.Dispatch(new EventActivityEntrance(activityType));
#endif
        }

        protected abstract void InitConfig(string config);
        protected abstract bool CheckUnlock();

        protected virtual void SubscribeEvents()
        {
            
        }
        public abstract UIWidgetBase CreateActivityEntrance(UIBase uiBase,  Transform widgetName);
        
        public string GetActivityId()
        {
            return activityId;
        }
        
        protected abstract string GetAssetLabelName();

        #region Download

        /// <summary>
        /// 创建资源下载器
        /// </summary>
        private ResourceDownloaderOperation CreateResourceDownloader(string[] tags)
        {
            var package = YooAssets.GetPackage(GlobalSetting.Ins.DefaultPackageName);
            var downloader = package.CreateResourceDownloader(tags, 3, 3);
            return downloader;
        }

        public void DownloadAsset()
        {
            CLog.Info($"{GetAssetLabelName()} ------ CheckUnlock : {CheckUnlock()}");
            if (CheckUnlock())
            {
                var assetLabel = GetAssetLabelName();
                CheckAndDownloadAssets(assetLabel);
            }
        }

        private async void CheckAndDownloadAssets(string assetLabel)
        {
            var result = GameGlobal.GetMgr<ResMgr>().CheckTagNeedDownload(assetLabel);

            if (result == HasAssetResult.AssetOnline)
            {
                CLog.Info($"Begin download {assetLabel}");
                var downloader = CreateResourceDownloader(new string[] {assetLabel});
                downloader.BeginDownload();
                await downloader.Task;

                if (downloader.Status == EOperationStatus.Succeed)
                {
                    CLog.Info($"Activity ------ Download {assetLabel} succeed!!!!!!!!!");
                    SetAssetDownloadStatus();
                }
                else
                {
                    CLog.Info($"Activity ------ Download {assetLabel} failed!!!!!!!!!");
                }
            }
            else
            {
                if (result == HasAssetResult.Valid)
                {
                    CLog.Info($"Activity ------ No {assetLabel} AssetsBundle----------");
                }
                else
                {
                    CLog.Info($"Activity ------ Download {assetLabel} succeed!!!!!!!!!");
                    SetAssetDownloadStatus();
                }
            }
        }

        public virtual void SetAssetDownloadStatus()
        {
            if (downloadStatus)
            {
                return;
            }
            var activityAssetLabel =  GetAssetLabelName();
            var result = GameGlobal.GetMgr<ResMgr>().CheckTagNeedDownload(activityAssetLabel);
            if (result == HasAssetResult.AssetOnDisk)
            {
                CLog.Info($"{activityAssetLabel} set download status true");
                downloadStatus = true;
                EventBus.Dispatch(new EventActivityEntrance(activityType));
            }
            else
            {
                CLog.Info($"{activityAssetLabel} set download status false");
            }
        }

        #endregion

        protected virtual void SetCollectingTimeCallback()
        {
            var leftTime = GetActivityLeftTime();
            if (leftTime / 1000 > 0)
            {
                TMUtility.WaitSeconds(leftTime / 1000,
                    () => { EventBus.Dispatch(new EventActivityUpdate(activityType)); }, default, true).Forget();
            }
        }

        /// <summary>
        /// 使用CountDown倒计时的活动按各活动需求重写此方法
        /// </summary>
        protected virtual void SetExpireTimeCallback()
        {
            var leftTime = GetActivityRewardLeftTime();
            if (_cancellation != null)
            {
                _cancellation.Cancel();
                _cancellation = null;
            }

            _cancellation = new CancellationTokenSource();

            TMUtility.WaitSeconds(leftTime / 1000, OnExpire, _cancellation.Token, true).Forget();
        }

        protected virtual void OnExpire()
        {
            GameGlobal.GetMod<ActivitySys>().RemoveActivity(activityType, activityId);

            EventBus.Dispatch(new EventActivityExpire(activityType, activityId));

            CleanUp();
        }

        /// <summary>
        /// 活动剩余时间
        /// </summary>
        /// <returns></returns>
        public ulong GetActivityLeftTime()
        {
            var left = (long) EndTime - (long) SDK<INetwork>.Instance.GetServerTime();
            if (left < 0)
                left = 0;
            return (ulong) left;
        }

        /// <summary>
        /// 活动开启状态
        /// </summary>
        /// <returns></returns>
        public virtual bool IsActivityOpened()
        {
            if (!_initedFromServer)
                return false;
            var startTimeOk = SDK<INetwork>.Instance.GetServerTime() > StartTime;
            var endTimeOk = SDK<INetwork>.Instance.GetServerTime() < EndTime;
            return downloadStatus && !ManualEnd && startTimeOk && endTimeOk;
        }

        /// <summary>
        /// 活动领奖剩余时间
        /// </summary>
        /// <returns></returns>
        public ulong GetActivityRewardLeftTime()
        {
            var left = (long) RewardEndTime - (long) SDK<INetwork>.Instance.GetServerTime();
            if (left < 0)
                left = 0;
            return (ulong) left;
        }

        /// <summary>
        /// 活动是否在领奖期间
        /// </summary>
        /// <returns></returns>
        public virtual bool IsActivityInReward()
        {
            if (!_initedFromServer)
                return false;

            var startTimeOk = SDK<INetwork>.Instance.GetServerTime() > EndTime;
            var endTimeOk = SDK<INetwork>.Instance.GetServerTime() < RewardEndTime;

            return downloadStatus && !ManualEnd && startTimeOk && endTimeOk;
        }

        /// <summary>
        /// 活动剩余时间的字符串显示
        /// </summary>
        /// <returns></returns>
        public string GetActivityLeftTimeString()
        {
            return TMUtility.FormatLongToTimeStr((long) GetActivityLeftTime(), false);
        }

        private void CleanUp()
        {
            DisableUpdate();
            CleanAllSubscribedEvents();
        }

        #region Events

        protected List<EventBus.Listener> listeners;

        /// <summary>
        /// 订阅游戏内事件
        /// </summary>
        /// <param name="handleAction"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public bool SubscribeEvent<T>(Action<T> handleAction) where T : IEvent
        {
            if (listeners == null)
            {
                listeners = new List<EventBus.Listener>();
            }

            var listener = EventBus.Subscribe<T>(handleAction);

            if (listener != null)
            {
                listeners.Add(listener);
                return true;
            }

            return false;
        }
        /// <summary>
        /// 取消事件订阅
        /// </summary>
        /// <param name="handleAction"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public bool UnsubscribeEvent<T>(Action<T> handleAction) where T : IEvent
        {
            if (listeners == null)
                return false;

            for (var i = 0; i < listeners.Count; i++)
            {
                if (((EventBus.DelegateListener) listeners[i]).eventHandler == (Delegate) handleAction)
                {
                    EventBus.Unsubscribe(listeners[i]);
                    listeners.RemoveAt(i);
                    return true;
                }
            }

            return false;
        }

        public void CleanAllSubscribedEvents()
        {
            if (listeners == null)
                return;
            for (var i = 0; i < listeners.Count; i++)
            {
                EventBus.Unsubscribe(listeners[i]);
            }

            listeners.Clear();
        }

        #endregion

        #region Update

        protected bool updateEnabled = false;

        /// <summary>
        /// 0:0.9f ,1:0.5f, 2:every frame
        /// </summary>
        /// <param name="updateInterval"></param>
        protected void EnableUpdate(int updateInterval = 0)
        {
            if (updateEnabled)
            {
                DisableUpdate();
            }

            updateEnabled = true;
        }

        protected void DisableUpdate()
        {
            if (updateEnabled)
            {
                updateEnabled = false;
            }
        }

        protected virtual void Update()
        {
        }

        #endregion
    }
}