using System;
using System.Linq;
using Cysharp.Threading.Tasks;
using DecorationRom;
using DecorationRom.Event;
using DragonPlus.Core;
using DragonPlus.Save;
using DragonU3DSDK.Network.API.Protocol;
using Framework;
using MergeHotupdate.Game;
using TMGame;
using TMGame.Storage;
using UnityEngine;




/// <summary>
/// 主界面状态
/// </summary>
public class FsmState_Home : FsmStateBase
{
    public override async void OnEnter(params object[] objs)
    {
        BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventFteEnterLoginScreen);

        base.OnEnter(objs);

        GameGlobal.GetMgr<SoundMgr>().PlayBgMusic("bg_level_out_1");
        GameGlobal.GetMod<ModCamera>().AddOverlayCamera(Camera.main, 0);
        while (!ModABTest.isGetAbData)
        {
            await UniTask.DelayFrame(1);
            ModABTest.RequestWaitTime++;
            if (ModABTest.RequestWaitTime >= 45)
            {
                break;
            }
        }
        ModABTest.RequestWaitTime = 0;
        var guildGroup = GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.ABTestGuide);
        bool showGuide = SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep <= 0;
        if (guildGroup == EABTestGroup.Group2 && showGuide)
        {
            HomeMod.ShowGuideing = true;
            RoomManager.Instance.Hide();
            //直接进入关卡
            if (GameGlobal.GetMod<GuideSys>().GetCurGuideId() == "GUIDE_104")
            {
                GameGlobal.GetMod<GuideSys>().FinishCurrent(GuideTargetType.EnterLeve);
            }
            GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Game);
        }
        else
        {
            showGuide = false;
        }
        var roomStorage = SDK<IStorage>.Instance.Get<StorageRoomCommon>();
        // 老玩家需要进行一次装修存档兼容操作
        if (!roomStorage.IsResetStorage)
        {
            try
            {
                RoomStorageExchange();
                roomStorage.IsResetStorage = true;
            }
            catch (Exception e)
            {
                roomStorage.IsResetStorage = false;
                CLog.Error("存档覆盖失败，修改标记等待下次重新覆盖");
                throw;
            }
        }
        
        int roomId = RoomManager.Instance.GetLastRoomId();
        bool roomEntered = false;
        bool isEnterDefault = false;
       if (RoomResSystem.Instance.IsRoomResReady(roomId))
        {
            RoomManager.Instance.EnterRoom(roomId, enterCall: (s) =>
            {
                if (s)
                {
                    roomEntered = true;
                    UIView_Boot.roomEntered = true;
                    RoomManager.Instance.CurRoomId = roomId;
                }
                else
                {
                    Debug.LogError($"进入房间失败了，会卡在loading进不去游戏 房间ID {roomId}");
                }
            });
        }
        else
        {
            RoomManager.Instance.EnterRoom(RoomManager.Instance.GetFirstRoomId(), enterCall: (s) =>
            {
                if (s)
                {
                    roomEntered = true;
                    UIView_Boot.roomEntered = true;
                    RoomManager.Instance.CurRoomId = RoomManager.Instance.GetFirstRoomId();
                }
                else
                {
                    Debug.LogError("进入房间失败了，会卡在loading进不去游戏");
                }
            });

            // 静默下载当前房间资源
            RoomResSystem.Instance.CheckAndDownloadAssets(roomId);
           //  GameGlobal.GetMgr<ResMgr>().Download($"Room{roomId}").Forget();
        }
       
        CLog.Info($"---abtest:{guildGroup}");
        while (!roomEntered)
        {
            await UniTask.DelayFrame(1);
        }
        if (!showGuide)
        {
            GameGlobal.GetMod<HomeMod>().EnterHome();
        }
        HomeMod.ShowGuideing = false;
    }

    
    public override void OnExit()
    {
        base.OnExit();
        UIView_Boot.roomEntered = false;
        GameGlobal.GetMod<ModCamera>().RemoveOverlayCamera(Camera.main);
        GameGlobal.GetMgr<UIMgr>().Close(UIViewName.UIView_HomeMain, false);
        GameGlobal.GetMgr<UIMgr>().Close(UIViewName.UIView_CurrencyGroup, false);
        RoomManager.Instance.Hide();

    }

    public override void Dispose()
    {
        base.Dispose();
        RoomManager.Instance.Release();
    }

    // 因为装修资源全局修改，所以需要兼容线上玩家
    // 这是一个有风险的举动，需要仔细测试
    private void RoomStorageExchange()
    {
        var roomCommonStorage = SDK<IStorage>.Instance.Get<StorageRoomCommon>();
        if (roomCommonStorage == null)
        {
            CLog.Error("RoomStorageExchange: roomCommonStorage is null");
            return;
        }

        // 1. 已经解锁的房间数量
        var oldUnlockRoomCnt = roomCommonStorage.UnLockRoom.Count;
        CLog.Info($"RoomStorageExchange: oldUnlockRoomCnt = {oldUnlockRoomCnt}");

        // 2. 获取当前room_roomchapter.json的配置，找到id为1005的数据块
        var roomChapters = TableConfigManage.Instance.GetRoomChapters();
        if (roomChapters == null || roomChapters.Count == 0)
        {
            CLog.Error("RoomStorageExchange: roomChapters is null or empty");
            return;
        }

        var targetChapter = roomChapters.FirstOrDefault(chapter => chapter.id == 1005);
        if (targetChapter == null || targetChapter.roomIds == null || targetChapter.roomIds.Length == 0)
        {
            CLog.Error("RoomStorageExchange: targetChapter with id 1005 not found or roomIds is empty");
            return;
        }

        CLog.Info($"RoomStorageExchange: found chapter 1005 with {targetChapter.roomIds.Length} rooms");

        // 3. 直接修改现有的StorageRoomCommon，将roomIds的前oldUnlockRoomCnt个房间设置为解锁状态

        // 第一步：清空相关存储数据
        roomCommonStorage.RoomData.Clear();
        roomCommonStorage.UnLockRoom.Clear();

        // 第二步：重建解锁房间和RoomData
        int roomsToUnlock = Math.Min(oldUnlockRoomCnt, targetChapter.roomIds.Length);
        for (int i = 0; i < roomsToUnlock; i++)
        {
            int roomId = targetChapter.roomIds[i];
            bool isLastRoom = (i == roomsToUnlock - 1);

            // 设置房间为解锁状态
            roomCommonStorage.UnLockRoom.Add(roomId, roomId);

            // 创建房间存储数据
            var storageRoom = new StorageRoom()
            {
                Id = roomId,
                IsFinish = !isLastRoom, // 非最后房间为true
                IsClean = !isLastRoom, // 非最后房间为true
                IsPlayAnim = !isLastRoom, // 非最后房间为true
                IsGetAward = !isLastRoom, // 非最后房间为true
                LastProgress = isLastRoom ? 0 : 1 // 最后房间为0，其他为1
            };

            // 处理roomNodes：最后房间保持为空，已完成房间设置所有节点为Received状态
            if (!isLastRoom)
            {
                // 获取该房间的所有节点配置
                var roomNodeConfigs = TableConfigManage.Instance.GetTableRoomNode(roomId);
                if (roomNodeConfigs != null && roomNodeConfigs.Count > 0)
                {
                    // 获取房间的装修配置
                    int roomResId = TableConfigManage.Instance.GetRoomResId(roomId);
                    var roomViewConfig = TableConfigManage.Instance.GetRoomNodeViewConfig(roomResId);

                    foreach (var nodeConfig in roomNodeConfigs)
                    {
                        // 从配置中获取该节点的第一个非旧装修ID
                        string defaultSelectId = GetFirstNewItemId(roomViewConfig, nodeConfig.id);

                        var storageNode = new StorageNode()
                        {
                            Id = nodeConfig.id,
                            Status = (int)RoomItem.Status.Received, // 设置为已获得状态
                            SelectId = defaultSelectId // 设置从配置读取的默认装修ID
                        };
                        storageRoom.RoomNodes.Add(nodeConfig.id, storageNode);
                    }

                    CLog.Info($"RoomStorageExchange: created {roomNodeConfigs.Count} nodes for completed room {roomId}");
                }
            }
            // 最后房间的roomNodes保持为空，让系统正常初始化

            roomCommonStorage.RoomData.Add(roomId, storageRoom);
        }

        // 设置当前房间ID为最后一个解锁的房间
        if (roomsToUnlock > 0)
        {
            roomCommonStorage.CurRoomId = targetChapter.roomIds[roomsToUnlock - 1];
        }

        CLog.Info($"RoomStorageExchange: created {roomsToUnlock} room data entries");
    }

    /// <summary>
    /// 从房间配置中获取指定节点的第一个非旧装修ID
    /// </summary>
    private string GetFirstNewItemId(Home.Core.HomeViewConfig roomViewConfig, long nodeId)
    {
        if (roomViewConfig?.roomNodes == null)
            return "";

        // 通过TableConfigManage直接获取节点配置，不依赖Room对象
        var nodeConfig = TableConfigManage.Instance.GetRoomNode((int)nodeId);
        if (nodeConfig == null)
        {
            return "";
        }

        // 如果是清扫节点，返回空字符串
        if (nodeConfig.isClear)
        {
            return "";
        }

        // 查找对应的节点配置
        var nodeCfg = roomViewConfig.roomNodes.Find(x => x.id == nodeId);
        if (nodeCfg?.ItemCfgs == null || nodeCfg.ItemCfgs.Count == 0)
            return "";

        // 查找第一个非旧装修的ID
        foreach (var itemCfg in nodeCfg.ItemCfgs)
        {
            if (!itemCfg.oldFurItemCfg.isOld)
            {
                return itemCfg.id;
            }
        }

        // 如果没有找到非旧装修，返回第一个装修ID
        return nodeCfg.ItemCfgs[0].id;
    }
}