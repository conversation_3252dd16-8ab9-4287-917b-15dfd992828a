using System.Collections.Generic;
using UnityEngine;

public static class Const_Common
{
    // 
    public const string SortingLayer_MinusLayer1 = "-Layer1";
    public const string SortingLayer_MinusLayer2 = "-Layer2";
    public const string SortingLayer_MinusLayer3 = "-Layer3";
    public const string SortingLayer_MinusLayer4 = "-Layer4";
    public const string SortingLayer_MinusLayer5 = "-Layer5";
    public const string SortingLayer_Zero = "Default";
    public const string SortingLayer_Layer1 = "Layer1";
    public const string SortingLayer_Layer2 = "Layer2";
    public const string SortingLayer_Layer3 = "Layer3";
    public const string SortingLayer_Layer4 = "Layer4";
    public const string SortingLayer_Layer5 = "Layer5";


    #region 图集名称定义
    public const string GameAtlas = "GameAtlas";
    #endregion

    #region 游戏积分字体颜色列表
    //#4effff,#4cff79,#f480ff,#ff6780,#ffbf40,#ffffoo
    public static List<Color32> GameScoreWordColors = new List<Color32>
    { 
        new Color32(78,255,255,255), new Color32(76, 255, 121, 255) , 
        new Color32(244, 128, 255, 255), new Color32(255, 103, 128, 255), 
        new Color32(255, 191, 64, 255), new Color32(255, 255, 0, 255) 
    };
    #endregion
}