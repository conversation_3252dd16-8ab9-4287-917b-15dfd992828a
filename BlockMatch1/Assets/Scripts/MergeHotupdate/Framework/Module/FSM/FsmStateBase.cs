using System;
using System.Collections.Generic;
using Framework;

public abstract class FsmStateBase
{
    private Dictionary<Type, FsmStateBase> subStateDict = new Dictionary<Type, FsmStateBase>();

    protected FsmStateBase DefaultState { get; private set; } //默认子状态
    public FsmStateBase CurSubState { get; private set; } //当前子状态
    public FsmStateBase PreSubState { get; private set; } //上一个子状态

    protected bool isBoot;
    protected bool isPause;

    public void SetDefaultSubState<T>()
        where T : FsmStateBase, new()
    {
        var type = typeof(T);
        if (!subStateDict.TryGetValue(type, out var _state))
        {
            CLog.Error($"找不到此state，先添加state：{type.FullName}");
            return;
        }
        DefaultState = _state;
        CurSubState = _state;
    }

    public void StartSubFsm(params object[] objs)
    {
        if (isBoot)
            return;
        isBoot = true;
        if (CurSubState == null)
        {
            CLog.Error($"至少添加一个子状态才能启动子状态机");
            return;
        }
        CurSubState?.OnEnter(objs);
    }

    /// <summary>
    /// 添加子状态
    /// </summary>
    public void AddSubState<T>(params object[] objs)
        where T : FsmStateBase, new()
    {
        var type = typeof(T);
        if (subStateDict.ContainsKey(type))
        {
            CLog.Error($"子状态已存在，不能重复添加子状态，state：{type}");
            return;
        }

        var state = new T();

        // 默认第一个添加的状态为初始状态
        if (subStateDict.Count <= 0)
        {
            DefaultState = state;
            CurSubState = state;
        }

        subStateDict.Add(type, state);
        state.OnInit(objs);
    }

    /// <summary>
    /// 移除子状态
    /// </summary>
    public bool RemoveSubState<T>()
        where T : FsmStateBase
    {
        var type = typeof(T);
        bool ret = subStateDict.Remove(type);
        return ret;
    }

    public void RemoveAllSubState()
    {
        isBoot = false;
        CurSubState?.OnExit();
        foreach (var state in subStateDict.Values)
        {
            state?.Dispose();
        }
        subStateDict.Clear();
    }

    /// <summary>
    /// 切换状态
    /// </summary>
    public void ChangeSubState<T>(params object[] objs)
        where T : FsmStateBase
    {
        var type = typeof(T);
        ChangeSubState(type, objs);
    }

    /// <summary>
    /// 切换状态
    /// </summary>
    public void ChangeSubState(Type type, params object[] objs)
    {
        if (!isBoot)
        {
            CLog.Error($"子状态机未启动，先启动子状态机");
            return;
        }
        if (!subStateDict.TryGetValue(type, out var _state))
        {
            CLog.Error($"找不到此子state，先添加子state：{type.FullName}");
            return;
        }
        if (CurSubState.GetType() == type)
        {
            CLog.Error($"不能切换到自身状态，state：{type.FullName}");
            return;
        }

        PreSubState = CurSubState;
        CurSubState?.OnExit();
        CurSubState = _state;
        CurSubState?.OnEnter(objs);
    }

    public bool CheckSubState<T>()
        where T : FsmStateBase
    {
        var type1 = CurSubState.GetType();
        var type2 = typeof(T);
        var ret = type1 == type2;
        return ret;
    }

    public void Pause()
    {
        if (!isBoot)
            return;
        if (isPause)
            return;
        CurSubState?.OnPause();
    }

    public void Resume()
    {
        if (!isBoot)
            return;
        if (!isPause)
            return;
        CurSubState?.OnResume();
    }

    public virtual void OnInit(params object[] objs)
    {
    }

    public virtual void OnEnter(params object[] objs)
    {
    }

    public virtual void OnPause()
    {
    }

    public virtual void OnResume()
    {
    }

    public virtual void OnUpdate(float deltaTime)
    {
        if (!isBoot || isPause)
            return;
        CurSubState?.OnUpdate(deltaTime);
    }

    public virtual void OnLateUpdate(float deltaTime)
    {
        if (!isBoot || isPause)
            return;
        CurSubState?.OnLateUpdate(deltaTime);
    }

    public virtual void OnExit()
    {
    }

    public virtual void Dispose()
    {
        CurSubState?.OnExit();
        foreach (var state in subStateDict.Values)
        {
            state?.Dispose();
        }
        subStateDict.Clear();
    }
}