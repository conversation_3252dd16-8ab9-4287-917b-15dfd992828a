using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using DragonPlus.Core;
using Framework;
using TMGame;
using UnityEngine;
using UnityEditor;
using YooAsset;
using UEObject = UnityEngine.Object;

/// <summary>
/// 资源类型
/// </summary>
public enum EResourceType
{
    Prefab = 1,
    Texture,
    Audio,
    Spine,
}

public class ResMgr : IManager
{
    public int Priority => 1000;

    private Dictionary<string, AssetDTO> assetDic = new Dictionary<string, AssetDTO>();

    public ResourceDownloaderOperation Downloader { get; set; }
    public int downloadingMaxNum = 3;
    public int failedTryAgain = 3;

    private ResMgr()
    {
    }
    
    

    #region 检查资源状态

    public void UnloadAsset(object asset)
    {
        throw new System.NotImplementedException();
    }

    public void UnloadUnusedAssets()
    {
        //YooAssets.GetPackage(GlobalSetting.Ins.DefaultPackageName).UnloadUnusedAssets();
    }

    public void ForceUnloadAllAssets()
    {
        //YooAssets.GetPackage(GlobalSetting.Ins.DefaultPackageName).ForceUnloadAllAssets();
    }

    /// <summary>
    /// 创建资源下载器，用于下载当前资源版本所有的资源包文件。
    /// </summary>
    public ResourceDownloaderOperation CreateResourceDownloader()
    {
        var package = YooAssets.GetPackage(GlobalSetting.Ins.DefaultPackageName);
        Downloader = package.CreateResourceDownloader("BuildIn", downloadingMaxNum, failedTryAgain);
        return Downloader;
    }

    /// <summary>
    /// 创建资源下载器，用于下载当前资源版本所有的资源包文件。
    /// </summary>
    public ResourceDownloaderOperation CreateResourceDownloader(string[] tags)
    {
        var package = YooAssets.GetPackage(GlobalSetting.Ins.DefaultPackageName);
        Downloader = package.CreateResourceDownloader(tags, downloadingMaxNum, failedTryAgain);
        return Downloader;
    }

    public HasAssetResult HasAsset(string assetName)
    {
        if (string.IsNullOrEmpty(assetName))
        {
            throw new SDKException("Asset name is invalid.");
        }

        AssetInfo assetInfo = YooAssets.GetAssetInfo(assetName);

        if (!CheckLocationValid(assetName))
        {
            return HasAssetResult.Valid;
        }

        if (assetInfo == null)
        {
            return HasAssetResult.NotExist;
        }

        if (IsNeedDownloadFromRemote(assetInfo))
        {
            return HasAssetResult.AssetOnline;
        }

        return HasAssetResult.AssetOnDisk;
    }

    public HasAssetResult CheckTagNeedDownload(string tag)
    {
        var assetInfos = YooAssets.GetAssetInfos(tag);
        if (assetInfos != null && assetInfos.Length > 0)
        {
            for (int i = 0; i < assetInfos.Length; i++)
            {
                if (IsNeedDownloadFromRemote(assetInfos[i]))
                    return HasAssetResult.AssetOnline;
            }

            return HasAssetResult.AssetOnDisk;
        }

        return HasAssetResult.Valid;
    }

    /// <summary>
    /// 是否需要从远端更新下载。
    /// </summary>
    /// <param name="location">资源的定位地址</param>
    public bool IsNeedDownloadFromRemote(string location)
    {
        return YooAssets.IsNeedDownloadFromRemote(location);
    }

    /// <summary>
    /// 是否需要从远端更新下载。
    /// </summary>
    /// <param name="assetInfo">资源信息。</param>
    public bool IsNeedDownloadFromRemote(AssetInfo assetInfo)
    {
        return YooAssets.IsNeedDownloadFromRemote(assetInfo);
    }

    /// <summary>
    /// 检查资源定位地址是否有效。
    /// </summary>
    /// <param name="location">资源的定位地址</param>
    public bool CheckLocationValid(string location)
    {
        return YooAssets.CheckLocationValid(location);
    }

    #endregion 检查资源状态

    #region 对外接口部分

    /// <summary>
    /// 检查资源是否可以清理
    /// </summary>
    public void CheckReference()
    {
        List<string> list = new List<string>();
        foreach (var asset in assetDic)
        {
            if (asset.Value.CanRelease())
            {
                list.Add(asset.Key);
            }
        }

        //
        foreach (var k in list)
        {
            //清理此处的缓存
            assetDic[k].Dispose();
            assetDic.Remove(k);
        }
    }

    /// <summary>
    /// Gets the res.
    /// </summary>
    public ResReferenceDTO<T> GetRes<T>(string an, bool isDependDelay = false) where T : UEObject
    {
        return GetOfficialRes<T>(an, isDependDelay);
    }

    /// <summary>
    /// Gets the res.
    /// </summary>
    public void GetRes<T>(string an, Action<ResReferenceDTO<T>> callback, bool isDependDelay = false) where T : UEObject
    {
        GetOfficialRes<T>(an, callback, isDependDelay);
    }

    /// <summary>
    /// Gets the game object.
    /// </summary>
    public GoReferenceDTO GetGameObject(string an, bool isDependDelay = false)
    {
        return GetOfficialGo(an, isDependDelay);
    }

    /// <summary>
    /// Gets the game object.
    public void GetGameObject(string an, Action<GoReferenceDTO> callback, bool isDependDelay = false)
    {
        GetOfficialGo(an, callback, isDependDelay);
    }

    #endregion
    
    private Dictionary<string, ResourceDownloaderOperation> downloadOperationDict = new Dictionary<string, ResourceDownloaderOperation>(); //下载任务缓存
    private ResourcePackage package;
    /// <summary>
    /// 下载资源
    /// </summary>
    public async UniTask Download(string tag,
        Action<string, long> onStartDownloadFile = null, Action<int, int, long, long> onDownloadProgress = null,
        Action<bool, bool> onDownloadOver = null, Action<string, string> onDownloadError = null)
    {
        if (downloadOperationDict.ContainsKey(tag))
            return;

        HasAssetResult result = CheckTagNeedDownload(tag);
        if (result != HasAssetResult.AssetOnline)
        {
            CLog.Info($"不需要下载资源，tag：[{tag}]");
            onDownloadOver?.Invoke(false, true);
            var evt = new EvtDownloadOver();
            evt.isByDownload = false;
            evt.isSucceed = true;
            evt.tag = tag;
            EventBus.Dispatch(evt);
            return;
        }

        var downloader = package.CreateResourceDownloader(tag, downloadingMaxNum, failedTryAgain);
        int totalDownloadCount = downloader.TotalDownloadCount;
        long totalDownloadBytes = downloader.TotalDownloadBytes;
        float totalDownloadMB = totalDownloadBytes * 1f / (1024 * 1024);
        CLog.Info($"开始下载资源，tag：[{tag}]，资源总数量：{totalDownloadCount}，总大小（MB）：{totalDownloadMB}");
        downloader.OnStartDownloadFileCallback = (fileName, sizeBytes) =>
        {
            var evt = new EvtStartDownload();
            evt.fileName = fileName;
            evt.sizeBytes = sizeBytes;
            EventBus.Dispatch(evt);
            onStartDownloadFile?.Invoke(fileName, sizeBytes);
        };
        downloader.OnDownloadProgressCallback = (totalDownloadCount, currentDownloadCount, totalDownloadBytes, currentDownloadBytes) =>
        {
            var evt = new EvtDownloadProgress();
            evt.tag = tag;
            evt.totalDownloadCount = totalDownloadCount;
            evt.currentDownloadCount = currentDownloadCount;
            evt.totalDownloadBytes = totalDownloadBytes;
            evt.currentDownloadBytes = currentDownloadBytes;
            EventBus.Dispatch(evt);
            onDownloadProgress?.Invoke(totalDownloadCount, currentDownloadCount, totalDownloadBytes, currentDownloadBytes);
        };
        downloader.OnDownloadOverCallback = (isSucceed) =>
        {
            CLog.Info($"下载资源结束，tag：[{tag}]，下载结果：[{isSucceed}]");
            downloadOperationDict.Remove(tag);
            onDownloadOver?.Invoke(true, isSucceed);
            var evt = new EvtDownloadOver();
            evt.isByDownload = true;
            evt.isSucceed = isSucceed;
            evt.tag = tag;
            EventBus.Dispatch(evt);
        };
        downloader.OnDownloadErrorCallback = (fileName, error) => { onDownloadError?.Invoke(fileName, error); };
        downloader.BeginDownload();
        downloadOperationDict.Add(tag, downloader);
        await downloader.Task;
    }


    /// <summary>
    /// 同步获取不需要instance初始化的资源
    /// </summary>
    private ResReferenceDTO<T> GetOfficialRes<T>(string an, bool isDependDelay = false) where T : UEObject
    {
        //string assetName = an.ToLower();
        string assetName = an;
        AssetDTO ass;
        assetDic.TryGetValue(assetName, out ass);
        if (ass == null)
        {
            AssetOperationHandle handle = YooAssets.LoadAssetSync<T>(assetName);
            ass = new AssetDTO(assetName, handle);
            assetDic.Add(assetName, ass);
        }
        return new ResReferenceDTO<T>(ass);
    }

    /// <summary>
    /// 异步获取不需要instance初始化的资源
    /// </summary>
    private void GetOfficialRes<T>(string an, Action<ResReferenceDTO<T>> callback, bool isDependDelay = false)
        where T : UEObject
    {
        AssetDTO ass;
        ResReferenceDTO<T> res;
        //string assetName = an.ToLower();
        string assetName = an;

        assetDic.TryGetValue(assetName, out ass);

        if (ass == null)
        {
            YooAssets.LoadAssetAsync<T>(assetName).Completed += handle =>
            {
                assetDic.TryGetValue(assetName, out ass);
                if (ass == null)
                {
                    ass = new AssetDTO(assetName, handle);
                    assetDic.Add(assetName, ass);
                }

                res = new ResReferenceDTO<T>(ass);
                callback(res);
            };
        }
        else
        {
            res = new ResReferenceDTO<T>(ass);
            callback(res);
        }
    }

    /// <summary>
    /// 同步获取gameObject对象
    /// </summary>
    private GoReferenceDTO GetOfficialGo(string an, bool isDependDelay = false)
    {
        //string assetName = an.ToLower();
        string assetName = an;
        AssetDTO ass;
        assetDic.TryGetValue(assetName, out ass);
        if (ass == null)
        {
            ass = new AssetDTO(assetName, YooAssets.LoadAssetSync<GameObject>(assetName));
            assetDic.Add(assetName, ass);
        }
        return new GoReferenceDTO(ass);
    }

    /// <summary>
    /// 异步获取gameObject对象
    /// </summary>
    private void GetOfficialGo(string an, Action<GoReferenceDTO> callback, bool isDependDelay = false)
    {
        AssetDTO ass = null;
        GoReferenceDTO gr;
        //string assetName = an.ToLower();
        string assetName = an;

        assetDic.TryGetValue(assetName, out ass);

        //Debug.Log("Reference  assetDTO 缓存：" + assetName);
        if (ass == null)
        {
            YooAssets.LoadAssetAsync<GameObject>(assetName).Completed += handle =>
            {
                assetDic.TryGetValue(assetName, out ass);
                if (ass == null)
                {
                    ass = new AssetDTO(assetName, handle);
                    assetDic.Add(assetName, ass);
                }

                gr = new GoReferenceDTO(ass);
                callback(gr);
            };
        }
        else
        {
            gr = new GoReferenceDTO(ass);
            callback(gr);
        }
    }
    public void Init()
    {
        package = YooAssets.GetPackage(GlobalSetting.Ins.DefaultPackageName);
    }
    public void Start()
    {
    }
    public void Update()
    {
    }

    public void Dispose()
    {
    }
}