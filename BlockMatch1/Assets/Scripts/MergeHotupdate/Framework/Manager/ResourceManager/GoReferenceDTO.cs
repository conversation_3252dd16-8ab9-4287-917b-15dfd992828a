using UnityEngine;

public class GoReferenceDTO
{
    private AssetDTO asset;

    public GoReferenceDTO(AssetDTO assetDTO)
    {
        asset = assetDTO;
    }

    public GameObject GetInstance(Transform parent = null)
    {
        if (asset == null) return null;
        GameObject go = asset.asset.InstantiateSync(parent);
        if (go == null)
            return null;
        //增加引用计数
        asset.AddReference(go);
        AddResReferenceHolder(go);
        return go;
    }

    /// <summary>
    /// 这里传入的go就是生成的go
    /// </summary>
    /// <param name="go">Go.</param>
    public void DelInstance(GameObject go)
    {
        if (go == null)
            return;
        asset.RemoveReference(go);
        Object.Destroy(go);
    }

    private void AddResReferenceHolder(GameObject goHolder)
    {
        AssetAutoGCMonoBehaviour autoGC = goHolder.GetComponent<AssetAutoGCMonoBehaviour>();
        if (autoGC == null)
        {
            autoGC = goHolder.AddComponent<AssetAutoGCMonoBehaviour>();
        }
        autoGC.AddAssetDTO(asset);
    }
}