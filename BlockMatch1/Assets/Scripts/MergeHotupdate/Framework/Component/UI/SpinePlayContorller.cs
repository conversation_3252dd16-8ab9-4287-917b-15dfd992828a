using System;
using UnityEngine.EventSystems;
using DG.Tweening;
using TMGame;
using UnityEngine;
using UnityEngine.UI;
using Spine.Unity;
using Spine;

public class SpinePlayContorller : MonoBehaviour
{
    [SerializeField]
    private float playDelay = 0;

    SkeletonGraphic skeletonGraphic;

    private CoroutineHandler _delayCoroutine;

    protected void Start()
    {
        skeletonGraphic = GetComponent<SkeletonGraphic>();
        if (skeletonGraphic != null && playDelay > 0)
        {
            this.gameObject.SetActive(false);
            if (_delayCoroutine != null) GameGlobal.GetMod<ModCoroutine>().StopCoroutine(_delayCoroutine);
            _delayCoroutine = GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(playDelay,
          () =>
          {
              _delayCoroutine = null;
              if (skeletonGraphic)
              {
                  skeletonGraphic.gameObject.SetActive(true);
              }
          }));
        }
    }


    protected void OnDestroy()
    {
        
    }
}