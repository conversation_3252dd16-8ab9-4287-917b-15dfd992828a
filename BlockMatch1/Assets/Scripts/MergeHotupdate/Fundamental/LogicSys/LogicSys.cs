// **********************************************
// Copyright(c) 2021 by com.ustar
// All right reserved
// 
// Author : <PERSON><PERSON><PERSON>
// Date : 2023/06/25/14:34
// Ver : 1.0.0
// Description : LogySys.cs
// ChangeLog :
// **********************************************

using System;
using System.Collections.Generic;
using DragonPlus.Core;

namespace TMGame
{
    public class LogicSys
    {
        public virtual int Priority => 0;

        #region Events

        protected List<EventBus.Listener> listeners;
        protected bool updateEnabled = false;
        protected bool lateUpdateEnabled = false;
        /// <summary>
        /// 订阅游戏内事件
        /// </summary>
        /// <param name="handleAction"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public bool SubscribeEvent<T>(Action<T> handleAction) where T : IEvent
        {
            if (listeners == null)
            {
                listeners = new List<EventBus.Listener>();
            }

            var listener = EventBus.Subscribe<T>(handleAction);

            if (listener != null)
            {
                listeners.Add(listener);
                return true;
            }
            return false;
        }

        // /// <summary>
        // /// 订阅游戏内有先后优先级的事件
        // /// </summary>
        // /// <param name="handleAction"></param>
        // /// <typeparam name="T"></typeparam>
        // /// <returns></returns>
        // public bool SubscribeEvent<T>(Action<Action, T, IEventHandlerScheduler> handleAction, int priority) where T : IEvent
        // {
        //     if (listeners == null)
        //     {
        //         listeners = new List<EventBus.Listener>();
        //     }
        //     
        //     var listener = EventBus.Subscribe<T>(handleAction, priority);
        //     
        //     if (listener != null)
        //     {
        //         listeners.Add(listener);
        //         return true;
        //     }
        //     return false;
        // }

        /// <summary>
        /// 取消订阅事件
        /// </summary>
        /// <param name="handleAction"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        // public bool UnsubscribeEvent<T>(Action<Action, T, IEventHandlerScheduler> handleAction) where T : IEvent
        // {
        //     if (listeners == null)
        //         return false;
        //
        //     for (var i = 0; i < listeners.Count; i++)
        //     {
        //         if (((EventBus.DelegateListener)listeners[i]).eventHandler == (Delegate) handleAction)
        //         {
        //             EventBus.Unsubscribe(listeners[i]);
        //             listeners.RemoveAt(i);
        //             return true;
        //         }
        //     }
        //     
        //     return false;
        // }

        /// <summary>
        /// 取消事件订阅
        /// </summary>
        /// <param name="handleAction"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public bool UnsubscribeEvent<T>(Action<T> handleAction) where T : IEvent
        {
            if (listeners == null)
                return false;

            for (var i = 0; i < listeners.Count; i++)
            {
                if (((EventBus.DelegateListener)listeners[i]).eventHandler == (Delegate)handleAction)
                {
                    EventBus.Unsubscribe(listeners[i]);
                    listeners.RemoveAt(i);
                    return true;
                }
            }

            return false;
        }

        public void CleanAllSubscribedEvents()
        {
            if (listeners == null)
                return;
            for (var i = 0; i < listeners.Count; i++)
            {
                EventBus.Unsubscribe(listeners[i]);
            }

            listeners.Clear();
        }

        #endregion

        public virtual void Init()
        {
            SubscribeEvents();
        }

        public virtual void Start()
        {
        }

        public virtual void SubscribeEvents()
        {
        }

        public virtual void Update()
        {
        }
        
        public virtual void Update(float deltaTime)
        {
        }

        public virtual void LateUpdate(float deltaTime)
        {
        }

        public virtual void OnShutDown()
        {
            DisableUpdate();
            CleanAllSubscribedEvents();
        }

        #region Obsolete：准备删掉了，不用再用了

        /// <summary>
        /// 0:0.9f ,1:0.5f, 2:every frame
        /// </summary>
        /// <param name="updateInterval"></param>
        public void EnableUpdate(int updateInterval = 0)
        {
            if (updateEnabled)
            {
                DisableUpdate();
            }
            if (updateInterval == 0)
                UpdateScheduler.HookSecondUpdate(Update);
            else if (updateInterval == 1)
                UpdateScheduler.HookHalfSecondUpdate(Update);
            else
                UpdateScheduler.HookUpdate(Update);
            updateEnabled = true;
        }

        public void DisableUpdate()
        {
            if (updateEnabled)
            {
                UpdateScheduler.UnhookUpdate(Update);
                updateEnabled = false;
            }
        }

        public void EnableLateUpdate()
        {
            if (lateUpdateEnabled)
            {
                DisableLateUpdate();
            }
            //UpdateScheduler.HookLateUpdate(LateUpdate);
            lateUpdateEnabled = true;
        }

        public void DisableLateUpdate()
        {
            if (lateUpdateEnabled)
            {
                //UpdateScheduler.UnhookLateUpdate(LateUpdate);
                lateUpdateEnabled = false;
            }
        }

        #endregion Obsolete：准备删掉了，不用再用了
    }
}