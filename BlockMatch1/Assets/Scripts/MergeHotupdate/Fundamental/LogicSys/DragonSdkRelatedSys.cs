// **********************************************
// Copyright(c) 2021 by com.ustar
// All right reserved
// 
// Author : <PERSON><PERSON><PERSON>
// Date : 2023/06/26/16:16
// Ver : 1.0.0
// Description : StorageSys.cs
// ChangeLog :
// **********************************************

using System.Collections.Generic;
using DragonPlus;
using DragonPlus.Core;
using DragonPlus.Save;
using TMGame.Storage;
using UnityEngine;

namespace TMGame
{
    public class DragonSdkRelatedSys : LogicSys
    {
        public override int Priority => 1000;

        //private bool isSetCustomKey=false;
        public override void Init()
        {
            base.Init();

            List<StorageBase> storageBases = new List<StorageBase>();
            storageBases.Add(new StorageCommon());
            storageBases.Add(new StorageGlobal());
            storageBases.Add(new StorageAd());
            storageBases.Add(new StorageRoomCommon());
            storageBases.Add(new StorageActivity());
            // storageBases.Add(new StorageGame());
            SDK<IStorage>.Instance.InitializeStorage(storageBases, true);
            //因操作频繁 在游戏频繁存档卡 改成5秒存档一次
            //StorageManager.Instance.LocalInterval = 5;

            // SDK.GetInstance().Initialize();

            LocalizationManager.Instance.MatchLanguage();

            var eventsHandler = GameObject.Find("SDKEventsHandler");

            if (eventsHandler == null)
            {
                eventsHandler = new GameObject("SDKEventsHandler");
            }

            if (eventsHandler.GetComponent<SDKEventsHandler>() == null)
            {
                eventsHandler.AddComponent<SDKEventsHandler>();
            }

            //EnableUpdate();
            SDK<IStorage>.Instance.Get<StorageCommon>().ResVersion = GameConfig.CurResVersion;
        }

        // public override void Update()
        // {
        //     if (!isSetCustomKey && FirebaseState.Instance.Initialized)
        //     {
        //         isSetCustomKey = true;
        //         Firebase.Crashlytics.Crashlytics.SetCustomKey("ResVersion", GlobalSetting.ResourceVersion);
        //         DisableUpdate();
        //     }
        // }
    }
}