using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using Cysharp.Threading.Tasks;
using DragonPlus.Core;
using DragonPlus.Network;
using DragonPlus.Save;
using DragonU3DSDK.Network.API.Protocol;
using Framework;
using Newtonsoft.Json;
using TMGame;
using TMGame.Storage;
using UnityEngine;
namespace Prediction
{
    public partial class PredictionManager : LogicSys
    {
        // 预测类型
        public enum PredictionTypes
        {
            churn,
            purchase,
        }

        public string ChurnPredictionVersion = "level_churn_v1"; // 流失预测版本
        public string PurchasePredictionVersion = "level_pay_v1"; // 付费预测版本

        const string Local_LevelInfos_Key = "local_levelinfos";
        const int SEND_MAX_COUNT = 10;
        public bool Inited
        {
            get;
            private set;
        }
        float refreshInterval = 2.0f;
        float refreshIntervalMax = 64.0f;
        float refreshTick = 0.0f;
        bool needRefresh = false;


        public override void Start()
        {
            base.Start();
            //LoadFromLocal();
            Inited = true;
            GetPredictionRemoteConfig();
            EventBus.Subscribe<ExitBlockPlayEvent>(OnExitBlockPlay);
        }

        public override void Update()
        {
            if (!Inited)
                return;
            //todo  不要了，只需要结束发
            return;
            if (!needRefresh)
            {
                refreshTick = 0f;
                return;
            }
            refreshTick += Time.deltaTime;
            if (refreshTick > refreshInterval)
            {
                refreshTick = 0.0f;
                if (refreshInterval < refreshIntervalMax)
                {
                    refreshInterval *= 2.0f;
                }
                SendReportPredictionEvent();
            }
        }

        public override void OnShutDown()
        {
            base.OnShutDown();
            Inited = false;
            refreshTick = 0.0f;
            EventBus.Unsubscribe<ExitBlockPlayEvent>(OnExitBlockPlay);
        }

        private PurchasePredictionParam GetNowPurchasePredictionParam()
        {
            var storageCommon = SDK<IStorage>.Instance.Get<StorageGlobal>();
            if (storageCommon == null)
            {
                return null;
            }
            return new PurchasePredictionParam()
            {
                //ad_cnt_3d = (uint)AdLogicManager.Instance.GetAdCount(3),
                //ad_cnt_5d = (uint)AdLogicManager.Instance.GetAdCount(5),
                //ad_cnt_7d = (uint)AdLogicManager.Instance.GetAdCount(7),
                //current_coin = (uint)ItemModel.Instance.GetNum((int)ResourceId.Coin),
                //current_diamond = (uint)ItemModel.Instance.GetNum((int)ResourceId.Gem),
                //current_fashion_coin = (uint)ItemModel.Instance.GetNum((int)ResourceId.AvatarCoin),
                //device_memory = DeviceHelper.GetTotalMemory().ToString(),
                //iap_count = storageCommon.RevenueCount,
                //iap_total = storageCommon.RevenueUSDCents,
                //installed_at = storageCommon.InstalledAt,
                //last_win_diff_coin = LastWinData.Instance.GetLastWinDiff((int)ResourceId.Coin),
                //last_win_diff_diamond = LastWinData.Instance.GetLastWinDiff((int)ResourceId.Gem),
                //last_win_get_coin = LastWinData.Instance.GetLastWinGetOrUse((int)ResourceId.Coin, 0),
                //last_win_get_diamond = LastWinData.Instance.GetLastWinGetOrUse((int)ResourceId.Gem, 0),
                //last_win_use_coin = LastWinData.Instance.GetLastWinGetOrUse((int)ResourceId.Coin, 1),
                //last_win_use_diamond = LastWinData.Instance.GetLastWinGetOrUse((int)ResourceId.Gem, 1),
                //max_asmr_level_id = (uint)ASMR.Model.Instance.GetASMRLevelId(),
                //max_sm_level_id = (uint)ScrewMatch.ScrewMatchModel.Instance.GetMainLevel(),
                //max_tm_level_id = (uint)TMatch.TMatchModel.Instance.GetMainLevel() - 1,
                //total_active_days = (uint)storageCommon.ActiveData.TotalActiveDays,
                //total_fail_cnt = (uint)storageHospital.TotalFailedCount,
                //total_level_cnt = (uint)storageHospital.TotalLevelCount,
                //total_win_cnt = (uint)ClientMgr.Instance.LevelCountTotal() - 1,
#if UNITY_ANDROID
                platform = "Android",
#elif UNITY_IOS
                platform = "iOS",
#endif
                //device_language = DeviceHelper.GetLanguage(), // 设备语言Spanish、Turkish...
                //device_country = storageCommon.Country,
                //setting_language = storageCommon.Locale, // 设置语言en、th...
                //is_facebook_bound = DragonU3DSDK.Account.AccountManager.Instance.HasBindFacebook() ? 1 : 0,
                //is_email_bound = DragonU3DSDK.Account.AccountManager.Instance.HasBindEmail() ? 1 : 0
            };
        }
        
        /// <summary>
        /// 请求获取付费预测结果
        /// </summary>
        public void GetPurchasePrediction()
        {
            var purchasePredictionParam = GetNowPurchasePredictionParam();
            if (purchasePredictionParam == null)
                return;
            var cGetConfig = new CGetPrediction();
            cGetConfig.PredictionType = PurchasePredictionVersion;
            cGetConfig.RealtimeInfo = ToJson(purchasePredictionParam);
            CLog.Info($"Prediction, purchase cGetConfig = {JsonConvert.SerializeObject(cGetConfig, Formatting.Indented)}" );
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            
            SDK<IRemoteRequest>.Instance.HandleRequest(cGetConfig, (SGetPrediction response) =>
            {
                stopwatch.Stop();
                var netTime = stopwatch.ElapsedMilliseconds;
                stopwatch = null;
                PredictionInfo(response.Prediction, netTime, PredictionTypes.purchase);
            },(errorCode, errorMsg, response) =>
            {
                stopwatch.Stop();
                var netTime = stopwatch.ElapsedMilliseconds;
                stopwatch = null;
                CLog.Error($"Prediction, Churn return error. errno= {errorCode} err = {errorMsg}, netTime= {netTime}");
               // BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventIappredictionIapvalue, "0", errorCode.ToString(), errorMsg, netTime.ToString());

            });
        }

        /// <summary>
        /// 请求获取流失预测结果
        /// </summary> 
        public void GetChurnPrediction()
        {
            var purchasePredictionParam = GetNowPurchasePredictionParam();
            if (purchasePredictionParam == null)
                return;
            var cGetConfig = new CGetPrediction();
            cGetConfig.PredictionType = ChurnPredictionVersion;
            cGetConfig.RealtimeInfo = ToJson(purchasePredictionParam);
            // cGetConfig.RealtimeInfo = "{}";
            CLog.Info($"Prediction, Churn cGetConfig = {JsonConvert.SerializeObject(cGetConfig, Formatting.Indented)}");
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            SDK<IRemoteRequest>.Instance.HandleRequest(cGetConfig, (SGetPrediction response) =>
            {
                
                stopwatch.Stop();
                var netTime = stopwatch.ElapsedMilliseconds;
                stopwatch = null;
                PredictionInfo(response.Prediction, netTime, PredictionTypes.churn);
                
            },(errorCode, errorMsg, response) =>
            {
                stopwatch.Stop();
                var netTime = stopwatch.ElapsedMilliseconds;
                stopwatch = null;
                CLog.Error($"Prediction, Churn return error. errno= {errorCode} err = {errorMsg}, netTime= {netTime}");
               // BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventChurnpredictionChurnvalue, "0", errorCode.ToString(), errorMsg, netTime.ToString());
            });
            
        }

        /// <summary>
        /// 处理预测返回结果
        /// </summary>
        /// <param name="predictionInfo"></param>
        /// <param name="netTime"></param>
        /// <param name="predictionType"></param>
        // / 失败：
        // {
        //   'info': 'player_id not find', 
        //   'player_id': '21', 
        //   'timeusage': 4, 
        //   'prediction': None, 
        //   'version': 'level_churn_v1'
        // }
        // 成功：
        // {
        //     "info": "inference",
        //     "player_id": "2670",
        //     "prediction": 0.791237,
        //     "timeusage": 73,
        //     "version": "level_pay_v1"
        // }
        // /// 失败：
        // {
        //   'info': 'player_id not find', 
        //   'player_id': '21', 
        //   'timeusage': 4, 
        //   'prediction': None, 
        //   'version': 'level_pay_v1'
        // }
        // 成功：
        // {
        //   "info": "inference",
        //   "player_id": "2670",
        //   "prediction": 0.791237,
        //   "timeusage": 73,
        //   "version": "level_pay_v1"
        // }
        private void PredictionInfo(string predictionInfo, long netTime, PredictionTypes predictionType)
        {
            if (string.IsNullOrEmpty(predictionInfo))
            {
                CLog.Warning($"Prediction, {predictionType}, CGetPrediction 服务器配置为空！");
                // if (predictionType == PredictionTypes.churn)
                //     BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventChurnpredictionChurnvalue, "1", "0", "", netTime.ToString());
                // else
                //     BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventIappredictionIapvalue, "1", "0", "", netTime.ToString());
                return;
            }
            try
            {
                bool _isValid = false;
                double _prediction = 0f;
                string _version = "";
                Hashtable table = JsonConvert.DeserializeObject<Hashtable>(predictionInfo);
                if (table != null
                                    && table["player_id"] != null && table["player_id"] is string player_id
                                    && table["prediction"] != null && table["prediction"] is double prediction
                                    && table["version"] != null && table["version"] is string version)
                {
                    _isValid = player_id == SDK<IStorage>.Instance.Get<StorageCommon>().PlayerId.ToString();
                    _prediction = prediction;
                    _version = version;
                    CLog.Error($"Prediction, {predictionType}, return data : isValid {_isValid}, prediction={_prediction}, version={_version}, netTime= {netTime}");
                }
                else
                {
                    CLog.Error($"Prediction, {predictionType},  Failed !!! : Prediction：: {predictionInfo}, netTime= {netTime}");
                }
                
                
                // if (predictionType == PredictionTypes.churn)
                //     BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventChurnpredictionChurnvalue, "1", _isValid ? _version : "0", _prediction.ToString(), netTime.ToString());
                // else
                //     BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventIappredictionIapvalue, "1", _isValid ? _version : "0", _prediction.ToString(), netTime.ToString());

                if (_isValid) // 数据有效才存下来
                {
                    var predictionData = SDK<IStorage>.Instance.Get<StorageGlobal>().PredictionInfo;
                    if (!predictionData.ContainsKey(predictionType.ToString()))
                        predictionData.Add(predictionType.ToString(), new StoragePrediction());
                    predictionData[predictionType.ToString()].Prediction = (float)_prediction;
                    predictionData[predictionType.ToString()].Version = _version;
                    // predictionData[predictionType.ToString()].UpdateTime = SDK<INetwork>.Instance.GetServerTime();
                }
                // 报一下测试模型的BI
                if (table != null && table["model_test_info"] != null)
                {
                    Hashtable testInfo = JsonConvert.DeserializeObject<Hashtable>(table["model_test_info"].ToString());
                    if (testInfo != null && testInfo["prediction"] != null && testInfo["prediction"] is double test_prediction
                                && testInfo["version"] != null && testInfo["version"] is string test_version)
                    {
                        // if (predictionType == PredictionTypes.churn)
                        //     BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventChurnpredictionChurnvalueTest, test_version, test_prediction.ToString());
                        // else
                        //     BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventIappredictionIapvalueTest, test_version, test_prediction.ToString());
                        CLog.Error($"Prediction, {predictionType}, return model_test_info : test_prediction={test_prediction}, test_version={test_version}");
                    }
                }
            }
            catch (Exception e)
            {
                // if (predictionType == PredictionTypes.churn)
                //     BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventChurnpredictionChurnvalue, "1", "0", "", netTime.ToString());
                // else
                //     BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventIappredictionIapvalue, "1", "0", "", netTime.ToString());
                CLog.Error($"Prediction, {predictionType},  Failed !!! : Prediction：: {predictionInfo}, netTime= {netTime}, error: {e}");
            }
        }

        public string ToJson(object data)
        {
            return JsonConvert.SerializeObject(data);
        }

        private BiEventBlockMatch1.Types.LevelInfo curSaveLevelInfo;
        public void AddLevelInfoData(BiEventBlockMatch1.Types.LevelInfo levelInfo)
        {
            CLog.Info("-----------AddLevelInfoData-----------");
            // needSendLevelInfos.Add(levelInfo);
            curSaveLevelInfo = levelInfo;
            // SaveToLocal();
        }


        // 上报
        public void SendReportPredictionEvent(Action<bool> callback = null)
        {
            CLog.Info("-----------SendReportPredictionEvent-----------");
            needRefresh = false;
            // if (needSendLevelInfos.Count <= 0)
            // {
            //     callback?.Invoke(true); // 不需要上报
            //     return;
            // }
            // isSendingLevelInfos = TrimList();
            // for (int i = 0; i < isSendingLevelInfos.Count; i++)
            // {
            //     CLog.Info($"Prediction ReportPredictionEvent, level_info[{i}] = {JsonConvert.SerializeObject(isSendingLevelInfos[i], Formatting.Indented)}");
            // }
            //needSendLevelInfos.Clear();
            // var SendEventData = new SendEventData<BiEventBlockMatch1.Types.LevelInfo>()
            // {
            //     data_type = "level_info",
            //     body = isSendingLevelInfos
            // };
            var sendData = new PredictionData()
            {
                time = GameUtils.GetServerTime().ToString(),
                current_coin = GameGlobal.GetMod<ModBag>().GetItemCount(EItemType.Coin).ToString(),
                current_energy = GameGlobal.GetMod<EnergySys>().GetEnergy().ToString(),
                max_area_id = RoomManager.Instance.CurRoomId.ToString(),
                max_level_id =StorageExtension.GameBlockLevelStorage?.CurLevelInfo.levelId.ToString(),
                offline = GameUtils.HasNetwork().ToString(),
                infinity_energy = GameGlobal.GetMod<EnergySys>().IsInfiniteEnergy().ToString(),
                current_direction = GameGlobal.GetMod<UserProfileSys>().GetItemCount(EItemType.Rotate).ToString(),
                current_bomb = GameGlobal.GetMod<UserProfileSys>().GetItemCount(EItemType.Bomb).ToString(),
                current_grid = GameGlobal.GetMod<UserProfileSys>().GetItemCount(EItemType.Convert).ToString(),
                level_count = curSaveLevelInfo.LevelCount.ToString(),
                level_id = curSaveLevelInfo.LevelId.ToString(),
                game_type = curSaveLevelInfo.GameType.ToString(),
                level_difficulty = curSaveLevelInfo.LevelDifficulty.ToString(),
                level_time = curSaveLevelInfo.LevelTime.ToString(),
                direction = curSaveLevelInfo.Direction.ToString(),
                bomb = curSaveLevelInfo.Bomb.ToString(),
                grid = curSaveLevelInfo.Grid.ToString(),
                difficult_puzzle = curSaveLevelInfo.DifficultPuzzle.ToString(),
                match_max = curSaveLevelInfo.MatchMax.ToString(),
                level_type = curSaveLevelInfo.LevelType.ToString(),
                level_result = curSaveLevelInfo.LevelResult.ToString(),
                level_progress = curSaveLevelInfo.LevelProgress.ToString(),
                level_algorithm = curSaveLevelInfo.LevelAlgorithm.ToString(),
                timing_buff = curSaveLevelInfo.TimingBuff.ToString(),
                time_respawn_count = curSaveLevelInfo.TimeRespawnCount.ToString(),
                time_respawn_ad_count = curSaveLevelInfo.TimeRespawnAdCount.ToString(),
                enter_time = curSaveLevelInfo.EnterTime.ToString(),
                energy_infinite = curSaveLevelInfo.EnergyInfinite.ToString(),
                blockgeneration_count = curSaveLevelInfo.BlockgenerationCount.ToString(),
                level_score = curSaveLevelInfo.LevelScore.ToString(),
            };
            
            var cGetConfig = new CReportPredictionEvent();
            cGetConfig.Event = ToJson(sendData); 
            CLog.Info($"Prediction ReportPredictionEvent = {JsonConvert.SerializeObject(cGetConfig, Formatting.Indented)}");
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            SDK<IRemoteRequest>.Instance.HandleRequest(cGetConfig, (SReportPredictionEvent response) =>
            {
                
                stopwatch.Stop();
                var netTime = stopwatch.ElapsedMilliseconds;
                stopwatch = null;
                CLog.Error($"Prediction ReportPredictionEvent send success. netTime= {netTime}");
                callback?.Invoke(true);
                
            },(errorCode, errorMsg, response) =>
            {
                stopwatch.Stop();
                var netTime = stopwatch.ElapsedMilliseconds;
                stopwatch = null;
                CLog.Error($"Prediction ReportPredictionEvent return error. errno= {errorCode} err = {errorMsg}, netTime= {netTime}");
                if (!Inited) return;
                //needSendLevelInfos.InsertRange(0, isSendingLevelInfos); // 发送失败的信息放在队列最前面，保证时序正确
                //isSendingLevelInfos.Clear();
                // //  关卡已经后不再用cd再次上报，等到结算的时候一起和BI一起上报
                // if (!(MyMain.myGame.Fsm.CurrentState is Gameplay.StateTinyGame stateTinyGame && Hospital.Game.Main.Instance.IsPlaying()))
                //     needRefresh = true;   // 等待cd再次上报
                callback?.Invoke(false);
            });

         
        }
        private void OnExitBlockPlay(ExitBlockPlayEvent args)
        {
            if (args.Type == 1|| args.Type ==1)
            {
                needRefresh = false;
                SendPrediction();
            }
        }

        public void SendPrediction()
        {
            // 预测上报
            SendReportPredictionEvent((bool success) =>
            {
                // 等待上报成功。算法同学建议上报成功后过1秒再请求数据。因为之前算法同学发现数据从预测服到入库成功会有几十毫秒延迟
                // 预测上报1s后再请求预测结果
                DelayReq().Forget();
            });
        }


        private async UniTaskVoid DelayReq()
        {
            await UniTask.Delay(1000);
            var mod = GameGlobal.GetMod<PredictionManager>();
            mod.GetChurnPrediction();  // 留存预测 先放这
            mod.GetPurchasePrediction();  // 付费预测 先放这
        }

        public class SendEventData<T>
        {
            public string data_type;
            public List<T> body;
        }

        public class PredictionData
        {
            public string time;
            public string current_coin;
            public string current_energy;
            public string max_area_id;
            public string max_level_id;
            public string offline;
            public string infinity_energy;
            public string current_direction;
            public string current_bomb;
            public string current_grid;
            
            public string level_count;
            public string level_id;
            public string game_type;
            public string level_difficulty;
            public string level_time;
            public string direction;
            public string bomb;
            public string grid;
            public string difficult_puzzle;
            public string match_max;
            public string level_type;
            public string level_result;
            public string level_progress;
            public string level_algorithm;
            public string timing_buff;
            public string time_respawn_count;
            public string time_respawn_ad_count;
            public string enter_time;
            public string energy_infinite;
            public string blockgeneration_count;
            public string level_score;

        }

        public class PurchasePredictionParam
        {
            public uint ad_cnt_3d; // 过去三天广告观看次数，3*24小时
            public uint ad_cnt_5d; // 过去五天广告观看次数，5*24小时
            public uint ad_cnt_7d; // 过去七天广告观看次数，7*24小时
            public uint current_coin; // 当前金币存量
            public uint current_diamond; // 当前钻石存量
            public uint current_fashion_coin; // 当前时装币存量
            public string device_memory; // 设备内存，与bi一致
            public uint iap_count; // 历史付费次数
            public ulong iap_total; // 历史付费金额
            public ulong installed_at; // 玩家安装时间
            public string last_win_diff_coin; // 上次胜利与当前金币差，没用统计到的时候传null
            public string last_win_diff_diamond; // 上次胜利与当前钻石差，没用统计到的时候传null
            public string last_win_get_coin; // 上次胜利至今金币总增量，没用统计到的时候传null
            public string last_win_get_diamond; // 上次胜利至今钻石总增量，没用统计到的时候传null
            public string last_win_use_coin; // 上次胜利至今金币总使用量，没用统计到的时候传null
            public string last_win_use_diamond; // 上次胜利至今钻石总使用量，没用统计到的时候传null
            public uint max_asmr_level_id; // asmr关卡最大等级，未开放传0
            public uint max_sm_level_id; // sm关卡最大等级
            public uint max_tm_level_id; // tm关卡最大等级
            public uint total_active_days; // 自新增之日起总登陆天数
            public uint total_fail_cnt; // 自新增之日起关卡总失败次数，老玩家从0开始统计，算法侧与bi统计取max
            public uint total_level_cnt; // 自新增之日起进关总次数，老玩家从总通关数开始统计，算法侧与bi统计取max
            public uint total_win_cnt; //  自新增之日起关卡总成功次数，
            public string platform; // 填Android或者iOS
            public string device_language; // 设备语言Spanish、Turkish...
            public string device_country; // 设备国家DZ、ZA ...
            public string setting_language; // 设置语言en、th...
            public int is_facebook_bound; // 是否 绑定了fb
            public int is_email_bound; //是否 绑定了email 
        }
    }
}

