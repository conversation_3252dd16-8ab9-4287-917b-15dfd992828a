using System;
using System.Collections.Generic;
using DragonPlus.Config.Global;
using DragonPlus.Core;
using DragonPlus.Save;
using Framework;
using TMGame;
using TMGame.Storage;
using UnityEngine;

namespace Active.ActivityModel
{
    public class Model : Manager<Model>
    {
        private readonly List<string> _rightEntrance =new List<string>();
        private readonly List<string> _leftEntrance =new List<string>();
        private List<Table_Global_Icon> _info;
        private readonly Dictionary<string, Func<UIBase, Transform,UIWidgetBase>> _funcEntrance = new ();
        private readonly Dictionary<string, Func<string>> _funcActivityType = new ();
        private readonly Dictionary<string, Func<bool>> _funcOpenEntrance = new ();
        public void InitModel()
        {
            _funcEntrance.Clear();
            _funcActivityType.Clear();
            _funcOpenEntrance.Clear();
            _info = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_Global_Icon>();
            var infoRight = new List<Table_Global_Icon>();
            var infoLeft = new List<Table_Global_Icon>();
            foreach (var t in _info)
            {
                switch (t.Area)
                {
                    case 1:
                        infoLeft.Add(t);
                        break;
                    case 2:
                        infoRight.Add(t);
                        break;
                }
            }
            infoRight.Sort((a, b) => a.Order.CompareTo(b.Order));
            infoLeft.Sort((a, b) => a.Order.CompareTo(b.Order));
            foreach (var t in infoRight)
            {
                _rightEntrance.Add(t.IconId);
            }
            foreach (var t in infoLeft)
            {
                _leftEntrance.Add(t.IconId);
            }
            InitFunc();
        }

        private void InitFunc()
        {
            //限时活动需要注册类型
            // AddFuncActivityType(UIViewName.UIView_TripleGiftPack.ToString(), () => ActivityType.TripleGiftPack);
            // AddFuncActivityType(UIViewName.UIView_EndlessGiftPack.ToString(), () => ActivityType.EndlessGiftPack);
            // //常驻活动注册开启条件
            // AddOpenEntrance(UIViewName.UIView_EndlessGiftPackLT.ToString(),()=>EndlessGiftPack.Model.Instance.IsOpenEndlessGiftPackLT());
            // AddOpenEntrance(UIViewName.UIView_SignIn.ToString(),()=>Active.SingIn.Model.Instance.SingInEntrance());
            // AddOpenEntrance(UIViewName.UIView_FindTM_UIStarsBox.ToString(),OpenStarBox);
            // AddOpenEntrance(UIViewName.UIView_FindTM_UIGradeBox.ToString(),OpenGradeBox);
             AddOpenEntrance(UIViewName.UIView_NoADS.ToString(),()=>Active.NoAds.Model.Instance.NoAdsEntrance());
             AddOpenEntrance(UIViewName.UIView_NoADS1.ToString(),()=>Active.NoAds.Model.Instance.NoAdsEntranceGroupB());

             AddOpenEntrance(UIViewName.UIView_DailyTask.ToString(),()=>Active.DailyTask.Model.Instance.DailyTaskEntrance());
            // AddOpenEntrance(UIViewName.UIView_FindTM_ADboxMain.ToString(),()=>Active.UIADBox.Model.Instance.IsUnLock());
            // AddOpenEntrance(UIViewName.UIView_UIShopMain.ToString(),()=>true);
            // //活动入口创建
            // AddFuncEntrance(ActivityType.TripleGiftPack, (ui, tra) => ui.OpenUIWidget<UIWidget_TripleGiftPack>(tra, false));
            // AddFuncEntrance(ActivityType.EndlessGiftPack, (ui, tra) => ui.OpenUIWidget<UIWidget_EndlessGiftPack>(tra, false));
            // AddFuncEntrance(UIViewName.UIView_EndlessGiftPackLT.ToString(), (ui, tra) => ui.OpenUIWidget<UIWidget_EndlessGiftPackLT>(tra, false));
            // AddFuncEntrance(UIViewName.UIView_SignIn.ToString(), (ui, tra) => ui.OpenUIWidget<UIWidget_SignIn>(tra, false));
            // AddFuncEntrance(UIViewName.UIView_FindTM_UIStarsBox.ToString(), (ui, tra) => ui.OpenUIWidget<UIWidget_StarsBox>(tra, false));
            // AddFuncEntrance(UIViewName.UIView_FindTM_UIGradeBox.ToString(), (ui, tra) => ui.OpenUIWidget<UIWidget_GradeBox>(tra, false));
             AddFuncEntrance(UIViewName.UIView_NoADS.ToString(),(ui, tra) => ui.OpenUIWidget<UIWidget_NoADS>(tra, false));
             AddFuncEntrance(UIViewName.UIView_NoADS1.ToString(),(ui, tra) => ui.OpenUIWidget<UIWidget_NoADS1>(tra, false));

             AddFuncEntrance(UIViewName.UIView_DailyTask.ToString(),(ui, tra) => ui.OpenUIWidget<UIWidget_DailyTask>(tra, false));
            // AddFuncEntrance(UIViewName.UIView_FindTM_ADboxMain.ToString(), (ui, tra) => ui.OpenUIWidget<UIWidget_ADBox>(tra, false));
            // AddFuncEntrance(UIViewName.UIView_UIShopMain.ToString(), (ui, tra) => ui.OpenUIWidget<UIWidget_Shop>(tra, false));

        }
        
        public void AddOpenEntrance(string str, Func<bool> func)
        {
            _funcOpenEntrance.TryAdd(str, func);
        }
        
        public void AddFuncActivityType(string str, Func<string> func)
        {
            _funcActivityType.TryAdd(str, func);
        }
        
        public void AddFuncEntrance(string str, Func<UIBase,Transform,UIWidgetBase> func)
        {
            _funcEntrance.TryAdd(str, func);
        }
        
        public string GetActivityType(string index)
        {
            if (_funcActivityType.TryGetValue(index, out var value))
            {
                return value.Invoke();
            }
            return index;
        }

        public int GetEntranceArea(string index)
        {
            foreach (var t in _info)
            {
                if (t.IconId == index)
                {
                    return t.Area;
                }
            }
            return 0;
        }
        
        public List<string> GetRightEntrances()
        {
            return _rightEntrance;
        }
        
        public List<string> GetLeftEntrances()
        {
            return _leftEntrance;
        }
        private void OnDestroy()
        {
            _rightEntrance.Clear();
            _leftEntrance.Clear();
        }
        
        public bool IsResidencyEntrance(string activityId)
        {
            if (_funcOpenEntrance.TryGetValue(activityId, out var value))
            {
                return value.Invoke();
            }
            return false;
        }

        private bool OpenGradeBox()
        {
            TryToResetGradeBoxIndex();
            var curIndex = SDK<IStorage>.Instance.Get<StorageGlobal>().LevelChest.CurIndex;
            return curIndex <= MaxGradeBoxIndex();

        }
        private int MaxGradeBoxIndex()
        {
            // var info = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_FindTM_LevelChest>();
            // return info[^1].Id;
            return 0;
        }
        
        private void TryToResetGradeBoxIndex()
        {
            var storage = SDK<IStorage>.Instance.Get<StorageGlobal>().LevelChest;
            if (storage.IsResetIndex) return;
            storage.IsResetIndex = true;
            if (storage.CurIndex > MaxGradeBoxIndex())
            {
                storage.CurIndex = MaxGradeBoxIndex();
            }
        }
        
        private bool OpenStarBox()
        {
            TryToResetStarBoxIndex();
            var curIndex = SDK<IStorage>.Instance.Get<StorageGlobal>().StarChest.CurIndex;
            return curIndex <= MaxStarBoxIndex();

        }
        private int MaxStarBoxIndex()
        {
            // var info = Game.GetMgr<ConfigMgr>().GetConfigs<Table_FindTM_StarChest>();
            // return info[^1].Id;
            return 0;
        }
        
        private void TryToResetStarBoxIndex()
        {
            var storage = SDK<IStorage>.Instance.Get<StorageGlobal>().StarChest;
            if (storage.IsResetIndex) return;
            storage.IsResetIndex = true;
            if (storage.CurIndex > MaxStarBoxIndex())
            {
                storage.CurIndex = MaxStarBoxIndex();
            }
        }
        
        public UIWidgetBase GetActivityEntrance(UIBase uiBase,Transform tra, string activityId)
        {
            if (_funcEntrance.TryGetValue(activityId, out var value))
            {
                return value.Invoke(uiBase, tra);
            }
            return null;
        }
        
        
    }
}