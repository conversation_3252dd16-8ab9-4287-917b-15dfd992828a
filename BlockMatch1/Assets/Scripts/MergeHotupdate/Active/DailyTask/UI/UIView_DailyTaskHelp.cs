using Framework;
using UnityEngine.UI;

public class UIView_DailyTaskHelp : UIView_DailyTaskHelpBase
{
    protected override void RegisterGameEvent()
    {
        base.RegisterGameEvent();
        UIBtn_Close.onClick.AddListener(OnCLoseClick);
    }

    private void OnCLoseClick()
    {
        Close();
    }

    protected override void RemoveGameEvent()
    {
        base.RemoveGameEvent();
        UIBtn_Close.onClick.RemoveListener(OnCLoseClick);
    }
    
    
}
