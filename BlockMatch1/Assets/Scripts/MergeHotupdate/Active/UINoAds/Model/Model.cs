using DragonPlus.Core;
using DragonPlus.Save;
using Framework;
using TMGame;
using TMGame.Storage;

namespace Active.NoAds
{
    public class Model: Manager<Model>
    {
        private StorageRemoveAd _storageNoAds;
        public void InitModel()
        {
            _storageNoAds =  SDK<IStorage>.Instance.Get<StorageGlobal>().RemoveAd;
            InitNoAds();
        }

        private void InitNoAds()
        {
            var cfg= GameGlobal.GetMod<IAPSys>().GetRemoveAdCfg();
            if (cfg!=null)
            {
                if (cfg.RemoveAdsGiftId.Count>1)
                {
                    UIView_NoADS.OpenData openData = new UIView_NoADS.OpenData()
                    {
                        isPop = true,
                        shopId = cfg.RemoveAdsGiftId[1],
                        ShopIdB = cfg.RemoveAdsGiftId[0],
                    };
                    GameGlobal.GetMod<ModPopup>().AddPopup(UIViewName.UIView_NoADS, NoAdsPopup,openData);
                }
            }
            else
            {
                CLog.Error("去广告礼包配置NoAdsCfg is null");
            }
            
         
        }

        private bool NoAdsPopup()
        {
            if (!GameGlobal.GetMod<AdSys>().PlayedInterstitalDurLogin)
            {
                return false;
            }
            return IsUnLock();
        }

        public bool NoAdsEntrance()
        {
            return IsUnLock();
        }
        
        public bool NoAdsEntranceGroupB()
        {
            if (ModABTestEx.IPlayGroupB())
            {
                return false;
            }
            return IsUnLock();
        } 
        
        public bool IsUnLock()
        {
            if (GameGlobal.GetMod<AdSys>().IsRemoveAd())
            {
                return false;
            }
            var cfg = GameGlobal.GetMod<IAPSys>().GetRemoveAdCfg();
            if (StorageExtension.GameBlockLevelStorage.CurLevelInfo.levelId > cfg.UnlockLevel || StorageExtension.GameEndlssStorage.CurFirstScore >= cfg.UnlockScore)
            {
                return true;
            }

            return false;
        }
        
        
    }
}