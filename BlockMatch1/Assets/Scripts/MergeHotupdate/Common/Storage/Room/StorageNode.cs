/************************************************
 * Storage class : StorageNode
 * This file is can not be modify !!!
 * If there is some problem, ask bin.guo.
 ************************************************/

using System;
using DragonPlus.Save;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace  TMGame.Storage
{
    [System.Serializable]
    public class StorageNode : StorageBase
    {
        
        // 挂点ID
        [JsonProperty]
        long id;
        [JsonIgnore]
        public long Id
        {
            get
            {
                return id;
            }
            set
            {
                if(id != value)
                {
                    id = value;
                     Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
        // 挂点状态 1解锁 2获取
        [JsonProperty]
        int status;
        [JsonIgnore]
        public int Status
        {
            get
            {
                return status;
            }
            set
            {
                if(status != value)
                {
                    status = value;
                     Profile.Instance.UpdateLocalVersion();
                }
            }
        }
        // ---------------------------------//
        
        // 当前选择的ID
        [JsonProperty]
        string selectId = "";
        [JsonIgnore]
        public string SelectId
        {
            get
            {
                return selectId;
            }
            set
            {
                if(selectId != value)
                {
                    selectId = value;
                     Profile.Instance.UpdateLocalVersion();
                    
                }
            }
        }
        // ---------------------------------//
        
        // 是否一次点击逻辑
        [JsonProperty]
        bool oneTimeChoice;
        [JsonIgnore]
        public bool OneTimeChoice
        {
            get
            {
                return oneTimeChoice;
            }
            set
            {
                if(oneTimeChoice != value)
                {
                    oneTimeChoice = value;
                     Profile.Instance.UpdateLocalVersion();
                }
            }
        }
        // ---------------------------------//
        
    }
}