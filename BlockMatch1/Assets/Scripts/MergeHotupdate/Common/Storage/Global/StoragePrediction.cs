/************************************************
 * Storage class : StorageAd
 * This file is can not be modify !!!
 * If there is some problem, ask hong.zhou.
 ************************************************/

using System;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using DragonPlus.Save;

namespace TMGame.Storage
{
    [System.Serializable]
    public class StoragePrediction : StorageBase
    {             
        //流水预测模型版本
        [JsonProperty]
        string version;
        [JsonIgnore]
        public string Version
        {
            get
            {
                return version;
            }

            set
            {
                if (version == value) return;
                version = value;
                Profile.Instance.UpdateLocalVersion();
            }
        }
        // ---------------------------------//

        //流水预测值
        [JsonProperty]
        float prediction;
        [JsonIgnore]
        public float Prediction
        {
            get
            {
                return prediction;
            }

            set
            {
                if (prediction == value) return;
                prediction = value;
                Profile.Instance.UpdateLocalVersion();
            }
        }
        // ---------------------------------//
    }
}