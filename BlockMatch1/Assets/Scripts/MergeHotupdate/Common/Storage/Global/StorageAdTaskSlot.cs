/************************************************
 * Storage class : StorageAdTaskSlot
 * This file is can not be modify !!!
 * If there is some problem, ask hong.zhou.
 ************************************************/

using System;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using DragonPlus.Save;

namespace TMGame.Storage
{
    [System.Serializable]
    public class StorageAdTaskSlot : StorageBase
    {
        
        // 奖励物品ID
        [JsonProperty]
        StorageList<int> rewardsID = new StorageList<int>();
        [JsonIgnore]
        public StorageList<int> RewardsID
        {
            get
            {
                return rewardsID;
            }
        }
        // ---------------------------------//
        
        // 奖励物品数量
        [JsonProperty]
        StorageList<int> rewardsCount = new StorageList<int>();
        [JsonIgnore]
        public StorageList<int> RewardsCount
        {
            get
            {
                return rewardsCount;
            }
        }
        // ---------------------------------//
        
        // 领取次数
        [JsonProperty]
        int claimCount;
        [JsonIgnore]
        public int ClaimCount
        {
            get
            {
                return claimCount;
            }
            set
            {
                if(claimCount != value)
                {
                    claimCount = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
        // 奖励刷新次数
        [JsonProperty]
        int refreshCount;
        [JsonIgnore]
        public int RefreshCount
        {
            get
            {
                return refreshCount;
            }
            set
            {
                if(refreshCount != value)
                {
                    refreshCount = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
    }
}