/************************************************
 * Storage class : StorageAdTask
 * This file is can not be modify !!!
 * If there is some problem, ask hong.zhou.
 ************************************************/

using System;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using DragonPlus.Save;

namespace TMGame.Storage
{
    [System.Serializable]
    public class StorageAdTask : StorageBase
    {
        
        // 广告宝箱分组
        [JsonProperty]
        int groupId;
        [JsonIgnore]
        public int GroupId
        {
            get
            {
                return groupId;
            }
            set
            {
                if(groupId != value)
                {
                    groupId = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
        // 完成的广告任务
        [JsonProperty]
        int adsTaskCount;
        [JsonIgnore]
        public int AdsTaskCount
        {
            get
            {
                return adsTaskCount;
            }
            set
            {
                if(adsTaskCount != value)
                {
                    adsTaskCount = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
        // 广告任务重置时间
        [JsonProperty]
        long adsTaskResetTime;
        [JsonIgnore]
        public long AdsTaskResetTime
        {
            get
            {
                return adsTaskResetTime;
            }
            set
            {
                if(adsTaskResetTime != value)
                {
                    adsTaskResetTime = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
        // 广告任务CD截止时间
        [JsonProperty]
        long adsTaskCDFinishTime;
        [JsonIgnore]
        public long AdsTaskCDFinishTime
        {
            get
            {
                return adsTaskCDFinishTime;
            }
            set
            {
                if(adsTaskCDFinishTime != value)
                {
                    adsTaskCDFinishTime = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
        // 上一个广告任务完成时间
        [JsonProperty]
        long adsTaskFinishTime;
        [JsonIgnore]
        public long AdsTaskFinishTime
        {
            get
            {
                return adsTaskFinishTime;
            }
            set
            {
                if(adsTaskFinishTime != value)
                {
                    adsTaskFinishTime = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
        // 广告任务固化CD
        [JsonProperty]
        long adsTaskCacheCD;
        [JsonIgnore]
        public long AdsTaskCacheCD
        {
            get
            {
                return adsTaskCacheCD;
            }
            set
            {
                if(adsTaskCacheCD != value)
                {
                    adsTaskCacheCD = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
         
        // 每个槽位宝箱内部奖励列表
        [JsonProperty]
        StorageDictionary<int,StorageAdTaskSlot> adsTaskRewardList = new StorageDictionary<int,StorageAdTaskSlot>();
        [JsonIgnore]
        public StorageDictionary<int,StorageAdTaskSlot> AdsTaskRewardList
        {
            get
            {
                return adsTaskRewardList;
            }
        }
        // ---------------------------------//
        
        // 奖励包类型对应的刷新次数
        [JsonProperty]
        StorageDictionary<int,int> typeRefreshCount = new StorageDictionary<int,int>();
        [JsonIgnore]
        public StorageDictionary<int,int> TypeRefreshCount
        {
            get
            {
                return typeRefreshCount;
            }
        }
        // ---------------------------------//
        
    }
}