/************************************************
 * Storage class : StorageWeeklyChallenge
 * This file is can not be modify !!!
 * If there is some problem, ask hong.zhou.
 ************************************************/

using System;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using DragonPlus.Save;

namespace TMGame.Storage
{
    [System.Serializable]
    public class StorageWeeklyChallenge : StorageBase
    {
        
        // 当前周的ID
        [JsonProperty]
        int curWeekId;
        [JsonIgnore]
        public int CurWeekId
        {
            get
            {
                return curWeekId;
            }
            set
            {
                if(curWeekId != value)
                {
                    curWeekId = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
        // 当前等级
        [JsonProperty]
        int curLevel;
        [JsonIgnore]
        public int CurLevel
        {
            get
            {
                return curLevel;
            }
            set
            {
                if(curLevel != value)
                {
                    curLevel = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
        // 当前收集的物品数量
        [JsonProperty]
        int curCollectItemNum;
        [JsonIgnore]
        public int CurCollectItemNum
        {
            get
            {
                return curCollectItemNum;
            }
            set
            {
                if(curCollectItemNum != value)
                {
                    curCollectItemNum = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
        // 当前周领奖次数
        [JsonProperty]
        int curClaimCnt;
        [JsonIgnore]
        public int CurClaimCnt
        {
            get
            {
                return curClaimCnt;
            }
            set
            {
                if(curClaimCnt != value)
                {
                    curClaimCnt = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
        // 是否已经主动弹出
        [JsonProperty]
        long popup;
        [JsonIgnore]
        public long Popup
        {
            get
            {
                return popup;
            }
            set
            {
                if(popup != value)
                {
                    popup = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
    }
}