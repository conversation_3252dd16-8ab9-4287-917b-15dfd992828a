/************************************************
 * Config class : Table_InGame_BaseBoard
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.InGame
{
    public partial class Table_InGame_BaseBoard:ConfigBase
    {   
        /// <summary>
        /// #ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// #历史最高分数; 玩家历史最高分数满足数值范围读取对应的数值; 最小值 ≤ 最高分数 ＜ 最大值
        /// </summary>
        public List<int> BestScore { get; set; }
        
        /// <summary>
        /// #复杂度限制值; 判断生成初始棋盘复杂度数值小于该值则满足条件
        /// </summary>
        public int Complextiy { get; set; }
        
        /// <summary>
        /// #稀疏度限制值; 判断生成初始棋盘稀疏度数值在指定范围内则满足条件
        /// </summary>
        public List<int> Sparsity { get; set; }
        
        /// <summary>
        /// #行列复杂限制值; 判断生成初始棋盘行列复杂度数值在指定范围内则满足条件
        /// </summary>
        public List<int> RowColumn { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}