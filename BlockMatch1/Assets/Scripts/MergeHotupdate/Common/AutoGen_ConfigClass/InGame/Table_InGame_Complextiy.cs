/************************************************
 * Config class : Table_InGame_Complextiy
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.InGame
{
    public partial class Table_InGame_Complextiy:ConfigBase
    {   
        /// <summary>
        /// #ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 复杂度算法分组-默认; 100：基础配置; 200：简单配置; 300：B组基础配置; 400：B组小白配置
        /// </summary>
        public int ComplextiyGroup { get; set; }
        
        /// <summary>
        /// #体验类型; 1：轻松; 2：中等; 3：一点压力; 4：压力; 5：最高压力
        /// </summary>
        public int ExperienceType { get; set; }
        
        /// <summary>
        /// 复杂度区间; （大于等于最小值，小于最大值）
        /// </summary>
        public List<int> ComplextiyRange { get; set; }
        
        /// <summary>
        /// 复杂度增加
        /// </summary>
        public int ComplexityIncreaseWeight { get; set; }
        
        /// <summary>
        /// 复杂度减少
        /// </summary>
        public int ComplexityDecreaseWeight { get; set; }
        
        /// <summary>
        /// 随机算法
        /// </summary>
        public int RandomWeight { get; set; }
        
        /// <summary>
        /// 难题算法
        /// </summary>
        public int DifficultyWeight { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}