/************************************************
 * Config class : Table_InGame_BlockWeightsNew
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.InGame
{
    public partial class Table_InGame_BlockWeightsNew:ConfigBase
    {   
        /// <summary>
        /// #ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 历史最高分下限
        /// </summary>
        public int History_score_min { get; set; }
        
        /// <summary>
        /// 历史最高分上限
        /// </summary>
        public int History_score_max { get; set; }
        
        /// <summary>
        /// 复杂度下限
        /// </summary>
        public int Complexty_min { get; set; }
        
        /// <summary>
        /// 复杂度上限
        /// </summary>
        public int Complexty_max { get; set; }
        
        /// <summary>
        /// 出块列表
        /// </summary>
        public List<int> Block_ids { get; set; }
        
        /// <summary>
        /// 排序权重（无尽模式)
        /// </summary>
        public List<int> Weights { get; set; }
        
        /// <summary>
        /// 排序权重(关卡模式)
        /// </summary>
        public List<int> Weights_for_level { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}