/************************************************
 * Config class : Table_DailyTask_DailyTask
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.DailyTask
{
    public partial class Table_DailyTask_DailyTask:ConfigBase
    {   
        /// <summary>
        /// #ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// #任务排序，不包含可领取、已领取状态; （从小到大优先级越高）
        /// </summary>
        public int TaskOrder { get; set; }
        
        /// <summary>
        /// #任务类型; 1、无尽模式挑战次数（当日）; 2、无尽模式最大分数（当日）; 3、关卡模式通过关数（当日）
        /// </summary>
        public int TaskType { get; set; }
        
        /// <summary>
        /// 任务需求的数量
        /// </summary>
        public int TaskQuantity { get; set; }
        
        /// <summary>
        /// 奖励类型; 1、金币; 8、装修币; 10、无尽体力; 101、转换方向; 102、炸弹; 103、变成单块; 每个任务只能有1类奖励
        /// </summary>
        public List<int> RewardType { get; set; }
        
        /// <summary>
        /// 奖励数量
        /// </summary>
        public List<int> RewardNum { get; set; }
        
        /// <summary>
        /// 任务描述KEY
        /// </summary>
        public string TaskDescription { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}