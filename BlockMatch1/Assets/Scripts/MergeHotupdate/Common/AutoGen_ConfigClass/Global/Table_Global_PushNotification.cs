/************************************************
 * Config class : Table_Global_PushNotification
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.Global
{
    public partial class Table_Global_PushNotification:ConfigBase
    {   
        /// <summary>
        /// #
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 类型，ENUM
        /// </summary>
        public string Type { get; set; }
        
        /// <summary>
        /// 内容
        /// </summary>
        public string Text { get; set; }
        
        /// <summary>
        /// 按秒
        /// </summary>
        public int TimeInSecond { get; set; }
        
        /// <summary>
        /// 按天
        /// </summary>
        public int TimeInDay { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}