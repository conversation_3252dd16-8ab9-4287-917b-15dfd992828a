/************************************************
 * InGame Config Manager class : InGameConfigManager
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using TMGame;
using System.Reflection;
using Framework;
using DragonPlus.Core;


namespace DragonPlus.Config.InGame
{
    public partial class InGameConfigManager 
    {
        public static Table_InGame_OptimalConfiguration GetOptimalConfigurationConfig(int curScore, int churnGroup)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_OptimalConfiguration>();
            var theRes = theConfigs.Find(
                ele => curScore >= ele.CurrentScore && ele.OptimalId == churnGroup);
            return theRes;
        }

        public static Table_InGame_AlgorithmCorrection GetAlgorithmCorrectionConfig(int scoreRadio)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_AlgorithmCorrection>();
            var theRes = theConfigs.Find(ele => ele.ScoreRatio > scoreRadio);
            return theRes;
        }

        public static Table_InGame_BaseBoard GetBaseBoardConfig(int bestScore)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_BaseBoard>();
            var theRes = theConfigs.Find(ele => (ele.BestScore.Count == 2
                && bestScore >= ele.BestScore[0]
                && bestScore < ele.BestScore[1]));
            return theRes;
        }

        public static Table_InGame_BestScoreGroup GetBestScoreGroupConfig(int bestScore)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_BestScoreGroup>();
            var theRes = theConfigs.Find(ele => (ele.BestScore.Count == 2
                && bestScore >= ele.BestScore[0]
                && bestScore < ele.BestScore[1]));
            return theRes;
        }

        public static List<Table_InGame_ComplextiyVariable> GetComplextiyVariableList(int scoreGroup)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_ComplextiyVariable>();
            var theRes = theConfigs.FindAll(ele=>ele.ScoreGroup == scoreGroup);
            return theRes;
        }
        public static List<Table_InGame_Complextiy> GetComplexInfoByType(int feelType, int userGroupId)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_Complextiy>();
            List<Table_InGame_Complextiy> theRes = theConfigs.FindAll(
                ele => ele.ExperienceType == feelType
                && ele.ComplextiyGroup == userGroupId);
            
            return theRes;
        }

        public static Table_InGame_ComplextiyUpAlgorithm GetComplextiyUpAlgorithmConfig(int score, int churnGroup)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_ComplextiyUpAlgorithm>();
            return theConfigs.Find(ele => (ele.ScoreRange.Count == 2
                && score >= ele.ScoreRange[0]
                && score < ele.ScoreRange[1] && ele.ComplexityUpGroupId == churnGroup));
        }

        public static Table_InGame_ComplextiyDownAlgorithm GetComplextiyDownAlgorithmConfig(int score, int churnGroup)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_ComplextiyDownAlgorithm>();
            return theConfigs.Find(ele => (ele.ScoreRange.Count == 2
                && score >= ele.ScoreRange[0]
                && score < ele.ScoreRange[1] && ele.EliminateGroupId == churnGroup));
        }

        public static Table_InGame_ItemConfig GetIngameItemConfig(int itemId)
        {
            Table_InGame_ComplextiyDownAlgorithm ty = null;
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_ItemConfig>();
            return theConfigs.Find(ele => ele.ItemType == itemId);
        }

        public static Table_InGame_BlockRotate GetBlockRotateConfig(int configId)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_BlockRotate>();
            return theConfigs.Find(ele => ele.Id == configId);
        }

        public static List<Table_InGame_Chapter> GetChapterLevelList(int chapterId)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_Chapter>();
            return theConfigs.FindAll(ele => ele.ChapterId == chapterId);
        }

        public static Table_InGame_Chapter GetChapterLevelInfo(int levelId)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_Chapter>();
            return theConfigs.Find(ele => ele.LevelId == levelId);
        }


        public static Table_InGame_Score GetInGameScoreCfgByMatchNum(int matchNum)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_Score>();
            return theConfigs.Find(ele => ele.MatchNum == matchNum);
        }

        public static Table_InGame_Score GetInGameScoreCfgByMatchType(int matchType = 1)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_Score>();
            return theConfigs.Find(ele => ele.MatchType == matchType);
        }

        public static Table_InGame_Level GetInGameLevelCfgByLevelId(int levelId)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_Level>();

            // 获取ABTest分组
            EABTestGroup clientGroup = GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.abTestLevelGroup3);
            int targetAbTestGroup = clientGroup == EABTestGroup.Group2 ? 4 : 3;

            // 优先查找匹配ABTest分组的配置
            var levelConfig = theConfigs.Find(ele => ele.Sort == levelId && ele.AbTestGroup == targetAbTestGroup);

            // 如果没找到对应分组的配置，则使用默认分组(Group1)的配置
            if (levelConfig == null)
            {
                levelConfig = theConfigs.Find(ele => ele.Sort == levelId && ele.AbTestGroup == 1);
            }

            // 如果还是没找到，则使用第一个匹配ID的配置作为兜底
            if (levelConfig == null)
            {
                levelConfig = theConfigs.Find(ele => ele.Sort == levelId);
            }

            return levelConfig;
        }
        public static Table_InGame_StorySub GetInGameStorySubConfig(int storyGroup)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_StorySub>();
            var storyId = GameGlobal.GetMod<ModGame>().GetCurStoryId();
            var storyConfig = theConfigs.Find(ele => ele.GroudLevelId == storyGroup && ele.StoryId == storyId);
            if (storyConfig == null)
            {
                CLog.Error($"未找到对应的故事配置, storyGroup:{storyGroup}, storyId:{storyId}");
            }
            return storyConfig;
        }
        public static Table_InGame_AdvancedBlock GetInGameAdvancedBlockCfg(int id)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_AdvancedBlock>();
            if (theConfigs == null) return null;
            return theConfigs.Find(ele => ele.Id == id);
        }

        public static Table_InGame_BlockLevelGroup GetBlcokLevelGroupConfig(int score, int groupId)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_BlockLevelGroup>();
            var theInfo = theConfigs.Find(ele => score <= ele.ScoreNumber && ele.GroupId == groupId);
            return theInfo;

        }
        public static Table_InGame_ClearScreen GetInGameClearScreenConfig(int confId)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_ClearScreen>();
            return theConfigs.Find(ele => ele.Id == confId);
        }
        
        
        public static Table_InGame_BlockGroup GetBlcokGroupConfig(int score)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_BlockGroup>();
            var theInfo = theConfigs.Find(ele => score <= ele.ScoreNumber);
            return theInfo;

        }
        public static Table_InGame_ChapterReward GetChapterRewardConfig(int confId)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_ChapterReward>();
            return theConfigs.Find(ele => ele.Id == confId);
        }
        
        
        public static List<int> GetFilterBlockList(int complex)
        {
            var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_BasicRules>();

            var theInfo = theConfigs.Find((ele)=>
            {
                var judgeType = ele.JudgmentType;
                var theRes = false;
                switch (judgeType)
                {
                    case 0:
                        {
                            theRes = ele.ComplextiyNumber == complex;
                            break;
                        }
                    case 1:
                        {
                            theRes = ele.ComplextiyNumber < complex;
                            break;
                        }
                    case 2:
                        {
                            theRes = ele.ComplextiyNumber <= complex;
                            break;
                        }
                    case 3:
                        {
                            theRes = ele.ComplextiyNumber > complex;
                            break;
                        }
                    case 4:
                        {
                            theRes = ele.ComplextiyNumber >= complex;
                            break;
                        }
                    default:
                        break;
                }

                return theRes; 
            });

            if (theInfo != null) return theInfo.UnavailableBlockList;

            else return null;
        }


        static Table_InGame_Global IngameGlobalConfig;
        public static T GetGlobalConfig<T>(string sKey)
        {
            T res = default;
            try
            {
                if (IngameGlobalConfig == null)
                {
                    var theGlobalConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_Global>();
                    IngameGlobalConfig = theGlobalConfigs[0];
                }

                var theType = typeof(Table_InGame_Global);
                PropertyInfo property = theType.GetProperty(sKey);
                res = (T)property.GetValue(IngameGlobalConfig);
            }
            catch (Exception ex)
            {
                CLog.Exception($"GetGlobalConfig[{sKey}]对应的属性配置不存在, ex:{ex}");
            }
            
            return res;
        }
    }
}