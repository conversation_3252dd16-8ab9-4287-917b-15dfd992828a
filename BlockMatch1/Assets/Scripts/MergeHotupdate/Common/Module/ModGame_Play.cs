using System.Collections.Generic;
using TMGame;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;
using System;
using DragonPlus.Core;
using DragonPlus.Haptics;
using Framework;

public enum EnumBlockTip
{
    EBT_None = 0,
    EBT_ScoreNum,
    EBT_Terrific,
    EBT_Perfect,
    EBT_Combo,
    EBT_PatternFinished,
    EBT_Unbelievable,

    EBT_End,
}

public struct BlockTipInfo
{
    public EnumBlockTip tip;
    public int param1;
    public int param2;
    public int param3;

    public BlockTipInfo(EnumBlockTip tipType, int p1, int p2, int p3)
    {
        tip = tipType;
        param1 = p1;
        param2 = p2;
        param3 = p3;
    }
}


public partial class ModGame
{
    static string bombFrameAsset = "VFX_bomb_light2";
    static float tipPlayDuration = 1.0f;

    List<BlockTipInfo> blockTipInfos = new List<BlockTipInfo>();
    Dictionary<EnumBlockTip, Sequence> tipPlayDic = new Dictionary<EnumBlockTip, Sequence>();

    private CoroutineHandler _nexPlayCoroutine;
    // 2-3行消除音效
    private readonly string[] _dAndTClearAudiosNew = new string[]
    {
        "MaleUpbeatGameVoiceover_GreatJob1",
        "MaleUpbeatGameVoiceover_GreatJob2",
        "MaleUpbeatGameVoiceover_NiceJob1",
        "MaleUpbeatGameVoiceover_NiceJob2",
        "MaleUpbeatGameVoiceover_WayToGo1",
        "MaleUpbeatGameVoiceover_WayToGo2",
    };
    
    private readonly string[] _dAndTClearAudios = new string[]
    {
        "good",
        "nice_work",
        "great_job",
        "impressive",
        "lovely",
    };
    
    // 4行以上消除音效
    private readonly string[] _overFourClearAudios = new string[]
    {
        "perfect",
        "awsome",
        "spectacular",
    };
    
    private readonly string[] _overFourClearAudiosNew = new string[]
    {
        "MaleUpbeatGameVoiceover_Perfect1",
        "MaleUpbeatGameVoiceover_Perfect2",
        "MaleUpbeatGameVoiceover_Terrific1",
        "MaleUpbeatGameVoiceover_Terrific1",
        "MaleUpbeatGameVoiceover_Wow1",
        "MaleUpbeatGameVoiceover_Wow2",
    };
    
    private readonly string[] _incredibleAudios = new string[]
    {
        "VFX_Unbelievable",
    };

    private readonly string[] _incredibleAudiosNew = new string[]
    {
        "MaleUpbeatGameVoiceover_ThatsIncredible1",
        "MaleUpbeatGameVoiceover_ThatsIncredible2",
        "MaleUpbeatGameVoiceover_ThatsAmazing1",
        "MaleUpbeatGameVoiceover_ThatsAmazing2",
        "MaleUpbeatGameVoiceover_YouDidIt1",
        "MaleUpbeatGameVoiceover_YouDidIt2",
    };
    
    void PlayBlockTip()
    {
        if (blockTipInfos.Count <= 0) return;

        var theAction = new Action(() =>
        {
            var tempList = blockTipInfos.FindAll(ele => ele.tip <= EnumBlockTip.EBT_Perfect);
            
            // 如果有EBT_Unbelievable，则过滤掉EBT_Terrific和EBT_Perfect
            bool hasUnbelievable = blockTipInfos.Exists(tip => tip.tip == EnumBlockTip.EBT_Unbelievable);
            if (hasUnbelievable)
            {
                tempList = tempList.FindAll(ele => ele.tip != EnumBlockTip.EBT_Terrific && ele.tip != EnumBlockTip.EBT_Perfect);
            }
            
            foreach (var ele in tempList)
            {
                PlayBlockTip(ele);
            }
        });

        var theList1 = blockTipInfos.FindAll(ele => ele.tip > EnumBlockTip.EBT_Perfect);
        var theList2 = blockTipInfos.FindAll(ele => ele.tip <= EnumBlockTip.EBT_Perfect);
        bool bNeedPlayNext = theList2.Count > 0;
        if (theList1.Count > 0)
        {
            bool bHave = false;
            float theDelay = 0.7f;
            foreach (var ele in theList1)
            {
                var theRes = PlayBlockTip(ele);
                if (theRes)
                {
                    bHave = true;
                    if (bNeedPlayNext)
                    {
                        if (ele.tip == EnumBlockTip.EBT_Combo)
                        {
                            theDelay = Math.Max(theDelay, 0.7f);
                        }
                        else
                        {
                            theDelay = Math.Max(theDelay, 1.0f);
                        }
                    }
                }
            }

            if (bNeedPlayNext)
            {
                if (bHave == false)
                {
                    theAction.Invoke();
                }
                else
                {
                    if (_nexPlayCoroutine != null) GameGlobal.GetMod<ModCoroutine>().StopCoroutine(_nexPlayCoroutine);
                    _nexPlayCoroutine = GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(theDelay,
                  () =>
                  {
                      if (blockTipInfos.Count <= 0) return;
                      _nexPlayCoroutine = null;
                      theAction.Invoke();
                  }));
                }
            }
            
        }
        else
        {
            if (bNeedPlayNext) theAction.Invoke();
        }
    }

    Transform PlayBlockTip(BlockTipInfo bInfo)
    {
        Transform theRes = null;
        var theTipType = bInfo.tip;
        if (theTipType <= EnumBlockTip.EBT_None && theTipType >= EnumBlockTip.EBT_End) return theRes;
       
        if (tipPlayDic.ContainsKey(theTipType))
        {
            tipPlayDic[theTipType].Complete();
            tipPlayDic.Remove(theTipType);
        }
        
        switch (theTipType)
        {
            case EnumBlockTip.EBT_Combo:
                {
                    theRes = PlayComboEffect(bInfo.param1, bInfo.param2 > 0);
                    break;
                }
            case EnumBlockTip.EBT_ScoreNum:
                {
                    theRes = PlayBlockScore(bInfo.param1, bInfo.param2, bInfo.param3 > 0);
                    break;
                }
            default:
                {
                    theRes = PlayBlockTip(theTipType, (EnumGridColor)bInfo.param1);
                    break;
                }
        }

        if (theRes != null)
        {
            var _playSequence = DOTween.Sequence();
            _playSequence.Append(theRes.DOLocalMoveY(0, tipPlayDuration));
            _playSequence.SetUpdate(true);
            _playSequence.OnComplete(() =>
            {
                if (theRes == null) return;

                if (tipPlayDic.ContainsKey(theTipType))
                {
                    tipPlayDic[theTipType].Complete();
                    tipPlayDic.Remove(theTipType);
                }
                
                theRes.SetParent(curBlockPool);
                theRes.gameObject.SetActive(false);
            });

            tipPlayDic[bInfo.tip] = _playSequence;
        }

        return theRes;
    }

    Transform PlayComboEffect(int comboNum, bool isMax = false)
    {
        Transform theRes = null;
        if (comboNum <= 0) return theRes;

        string sAudio = "";
        switch (comboNum)
        {
            case 1:
                {
                    sAudio = "block_in_game_10";
                    break;
                }
            case 2:
                {
                    sAudio = "block_in_game_11";
                    break;
                }
            case 3:
                {
                    sAudio = "block_in_game_12";
                    break;
                }
            case 4:
                {
                    sAudio = "block_in_game_13";
                    break;
                }
            case 5:
                {
                    sAudio = "block_in_game_14";
                    break;
                }
            case 6:
                {
                    sAudio = "block_in_game_15";
                    break;
                }
            case 7:
                {
                    sAudio = "block_in_game_16";
                    break;
                }
            case 8:
                {
                    sAudio = "block_in_game_17";
                    break;
                }
            case 9:
                {
                    sAudio = "block_in_game_18";
                    break;
                }
            case 10:
                {
                    sAudio = "block_in_game_19";
                    break;
                }
            case 11:
                {
                    sAudio = "block_in_game_add_20";
                    break;
                }
            case 12:
                {
                    sAudio = "block_in_game_add_21";
                    break;
                }
            case 13:    
                {
                    sAudio = "block_in_game_add_22";
                    break;
                }
            case 14:
                {
                    sAudio = "block_in_game_add_23";
                    break;
                }
            case 15:
                {
                    sAudio = "block_in_game_add_24";
                    break;
                }
            default:
                {
                    sAudio = "block_in_game_add_25";
                    //sAudio = "block_in_game_9";
                    break;
                }
        }

        GameGlobal.GetMgr<SoundMgr>().PlaySfx(sAudio);

        //消除增加震动效果
        TMUtility.VibrateMatch();
        if (comboNum > 1 && CurGameType == EnumBlockGameType.BlockGame_Endless)
        {
            theRes = PlayComboEffectInner(comboNum, isMax);
        }

        return theRes;
    }

    Transform PlayComboEffectInner(int comboNum, bool isMax)
    {
        var theCombo = comboNum - 1;
        if(theCombo < 1) return null;

        var theAssetName = "VFX_Combo";
        var effectNode = curBlockPool.Find(theAssetName);
        if (effectNode == null)
        {
            var theGo = curBlockPool.CreateChild(theAssetName);
            effectNode = theGo.transform;
            var theAsset = GameGlobal.GetMgr<ResMgr>().GetRes<GameObject>(theAssetName).GetInstance(theGo);
            if (theAsset == null)
            {
                effectNode.parent = null;
                effectNode = null;
            }
            else
            {
                var theEffectGo = GameObject.Instantiate(theAsset, effectNode);
                theEffectGo.transform.localPosition = Vector3.zero;
                theEffectGo.transform.localScale = Vector3.one;
                theEffectGo.name = theAssetName;
            }
        }

        if (effectNode)
        {
            string theAnimation = "VFX_Combo2";
            var theRootTs = effectNode.Find(theAssetName);
            if (theRootTs != null)
            {
                var theTs = theRootTs.Find("root/Layout/number");
                var theComboTs = theRootTs.Find("root/Layout/combo");
                var theMaxTs = theRootTs.Find("root/Layout/max");
                if (theTs == null || theComboTs == null || theComboTs == null) return null;

                theComboTs.gameObject.SetActive(true);
                if (theCombo == 1)
                {
                    theAnimation = "VFX_Combo1";
                    theTs.gameObject.SetActive(false);
                    theMaxTs.gameObject.SetActive(false);

                    var thePos = theComboTs.localPosition;
                    thePos.x = 0;
                    theComboTs.localPosition = thePos;
                }
                else
                {
                    var thePos = theComboTs.localPosition;
                    thePos.x = -100;
                    theComboTs.localPosition = thePos;

                    // 原逻辑：根据isMax参数决定显示数字还是max文字
                    /*
                    theTs.gameObject.SetActive(isMax == false);
                    theMaxTs.gameObject.SetActive(isMax);
                    if (isMax == false)
                    {
                        List<int> ints = new List<int>();
                        var theComboNum = theCombo;
                        var tempNum = 10;
                        while (theComboNum > 0)
                        {
                            var theNumber = theComboNum % tempNum;
                            theComboNum = theComboNum / tempNum;
                            ints.Insert(0, theNumber);
                        }

                        var theNum = ints.Count;
                        var theChildCount = theTs.childCount;
                        for (int i = 0; i < theChildCount; i++)
                        {
                            var theChild = theTs.GetChild(i);
                            var theIndex = i;
                            if (theIndex < theNum && theIndex >= 0)
                            {
                                theChild.gameObject.SetActive(true);
                                var theImage = theChild.GetComponent<Image>();
                                var atlaspath = Const_Common.GameAtlas;
                                var iconPath = $"pz_number3_{ints[theIndex]}";
                                CoreUtils.SetImg(theImage, atlaspath, iconPath);
                            }
                            else
                            {
                                theChild.gameObject.SetActive(false);
                            }
                        }
                    }
                    */
                    
                    // 新逻辑：无尽模式下总是显示数字，不显示max状态
                    theTs.gameObject.SetActive(true);
                    theMaxTs.gameObject.SetActive(false);
                    
                    List<int> ints = new List<int>();
                    var theComboNum = theCombo;
                    var tempNum = 10;
                    while (theComboNum > 0)
                    {
                        var theNumber = theComboNum % tempNum;
                        theComboNum = theComboNum / tempNum;
                        ints.Insert(0, theNumber);
                    }

                    var theNum = ints.Count;
                    var theChildCount = theTs.childCount;
                    for (int i = 0; i < theChildCount; i++)
                    {
                        var theChild = theTs.GetChild(i);
                        var theIndex = i;
                        if (theIndex < theNum && theIndex >= 0)
                        {
                            theChild.gameObject.SetActive(true);
                            var theImage = theChild.GetComponent<Image>();
                            var atlaspath = Const_Common.GameAtlas;
                            var iconPath = $"pz_number3_{ints[theIndex]}";
                            CoreUtils.SetImg(theImage, atlaspath, iconPath);
                        }
                        else
                        {
                            theChild.gameObject.SetActive(false);
                        }
                    }
                }
            }

            effectNode.gameObject.SetActive(true);
            effectNode.SetParent(curTipPlayNode);
            effectNode.localPosition = Vector3.zero;
            var theAnimator = theRootTs.GetComponent<Animator>();
            if (theAnimator != null)
            {
                theAnimator.PlayAnim(theAnimation, null);
            }
        }

        return effectNode;
    }

    Transform PlayBlockScore(int score, int matchCount, bool isPlayBomSound)
    {
        Transform theRes = null;
        var theScore = score;
        if (theScore <= 0) return theRes;

        if (!isPlayBomSound)
        {
            GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_in_game_3");
        }
        else
        {
            // 爆破音效
            GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_in_game_25");
        }
        
        if (HasTargetType(EnumTargetType.ETT_Gem))
        {
            return theRes;
        }
        theRes = PlayBlockScoreInner(theScore, matchCount);
        return theRes;
    }

    Transform PlayBlockScoreInner(int score, int matchCount)
    {
        var theAssetName = "VFX_Score";
        var effectNode = curBlockPool.Find(theAssetName);
        if (effectNode == null)
        {
            var theGo = curBlockPool.CreateChild(theAssetName);
            effectNode = theGo.transform;
            var theAsset = GameGlobal.GetMgr<ResMgr>().GetRes<GameObject>(theAssetName).GetInstance(theGo);
            if (theAsset == null)
            {
                effectNode.parent = null;
                effectNode = null;
            }
            else
            {
                var theEffectGo = GameObject.Instantiate(theAsset, effectNode);
                theEffectGo.transform.localPosition = Vector3.zero;
                theEffectGo.transform.localScale = Vector3.one;
                theEffectGo.name = theAssetName;
            }
        }

        if (effectNode)
        {
            var theRootTs = effectNode.Find(theAssetName);
            if (theRootTs != null)
            {
                var theNumberTs = theRootTs.Find("root/number");
                var theTypoTs = theRootTs.Find("root/typo");
                if(theTypoTs) theTypoTs.gameObject.SetActive(false);
                if (theNumberTs == null) return null;
                theNumberTs.gameObject.SetActive(true);
                var theTs = theNumberTs;
                List<int> ints = new List<int>();
                var theScore = score;
                if (theScore > 999) theScore = 999;
                var tempNum = 10;
                while (theScore > 0)
                {
                    var theNumber = theScore % tempNum;
                    theScore = theScore / tempNum;
                    ints.Insert(0, theNumber);
                }

                var theNum = ints.Count;
                var theChildCount = theTs.childCount;
                for (int i = 0; i < theChildCount; i++)
                {
                    var theChild = theTs.GetChild(i);
                    if (theChild.name == "+")
                    {
                        theChild.gameObject.SetActive(true);
                        continue;
                    }
                    else
                    {
                        var theIndex = i - 1;
                        if (theIndex < theNum && theIndex >= 0)
                        {
                            theChild.gameObject.SetActive(true);
                            var theImage = theChild.GetComponent<Image>();
                            var atlaspath = Const_Common.GameAtlas;
                            var iconPath = $"pz_number1_{ints[theIndex]}";
                            CoreUtils.SetImg(theImage, atlaspath, iconPath);
                        }
                        else
                        {
                            theChild.gameObject.SetActive(false);
                        }
                    }
                }
            }


            effectNode.gameObject.SetActive(true);
            effectNode.SetParent(curTipPlayNode);
            effectNode.localPosition = Vector3.zero;          
        }

        return effectNode;
    }
    bool IsNewAudioByAB()
    {
        var group = GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.abTestAudio);
        return group == EABTestGroup.Group2;
    }
    Transform PlayBlockTip(EnumBlockTip type, EnumGridColor color = EnumGridColor.EGC_None)
    {
        Transform theRes = null;
        var theAssetName = "";
        var iconName = "";

        // 音效 abtest
        bool audioABTestNewGroup = IsNewAudioByAB();
        switch (type)
        {
            case EnumBlockTip.EBT_Terrific:
                {
                    theAssetName = "VFX_Terrific";
                    iconName = $"pz_terrific_{(int)color}";
                    if (audioABTestNewGroup)
                    {
                        var randomAudio = _dAndTClearAudiosNew[UnityEngine.Random.Range(0, _dAndTClearAudiosNew.Length)];
                        GameGlobal.GetMgr<SoundMgr>().PlaySfx(randomAudio);
                    }
                    else
                    {
                        var randomAudio = _dAndTClearAudios[UnityEngine.Random.Range(0, _dAndTClearAudios.Length)];
                        GameGlobal.GetMgr<SoundMgr>().PlaySfx(randomAudio);
                    }
                    break;
                }
            case EnumBlockTip.EBT_Perfect:
                {
                    theAssetName = "VFX_Perfect";
                    iconName = $"pz_perfect_{(int)color}";
                    if (audioABTestNewGroup)
                    {
                        var randomAudio = _overFourClearAudiosNew[UnityEngine.Random.Range(0, _overFourClearAudiosNew.Length)];
                        GameGlobal.GetMgr<SoundMgr>().PlaySfx(randomAudio);
                    }
                    else
                    {
                        var randomAudio = _overFourClearAudios[UnityEngine.Random.Range(0, _overFourClearAudios.Length)];
                        GameGlobal.GetMgr<SoundMgr>().PlaySfx(randomAudio);
                    }
                    break;
                }
            case EnumBlockTip.EBT_PatternFinished:
                {
                    theAssetName = "VFX_Pattern";
                    iconName = $"pz_perfect_{(int)color}";
                    //GameGlobal.GetMod<ModTip>().ShowEffect("VFX_Pattern");
                    GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_in_game_9");
                    break;
                }
            case EnumBlockTip.EBT_Unbelievable:
                {
                    theAssetName = "VFX_Unbelievable";
                    iconName = $"pz_perfect_{(int)color}";
                    //GameGlobal.GetMod<ModTip>().ShowEffect("VFX_Unbelievable");
                    if (audioABTestNewGroup)
                    {
                        var randomAudio = _incredibleAudios[UnityEngine.Random.Range(0, _incredibleAudios.Length)];
                        GameGlobal.GetMgr<SoundMgr>().PlaySfx(randomAudio);
                    }
                    else
                    {
                        var randomAudio = _incredibleAudiosNew[UnityEngine.Random.Range(0, _incredibleAudiosNew.Length)];
                        GameGlobal.GetMgr<SoundMgr>().PlaySfx(randomAudio);
                    }
                    break;
                }
        }

        if(!string.IsNullOrEmpty(theAssetName) && !string.IsNullOrEmpty(iconName))
        {
            theRes = PlayBlockTipInner(type, theAssetName, iconName);
        }
        
        return theRes;
    }

    Transform PlayBlockTipInner(EnumBlockTip type, string assetName, string iconName)
    {
        if (string.IsNullOrEmpty(assetName)) return null;

        var theAssetName = assetName;
        var effectNode = curBlockPool.Find(theAssetName);
        if (effectNode == null)
        {
            var theGo = curBlockPool.CreateChild(theAssetName);
            effectNode = theGo.transform;
            var theAsset = GameGlobal.GetMgr<ResMgr>().GetRes<GameObject>(theAssetName).GetInstance(theGo);
            if (theAsset == null)
            {
                effectNode.parent = null;
                effectNode = null;
            }
            else
            {
                var theEffectGo = GameObject.Instantiate(theAsset, effectNode);
                theEffectGo.transform.localPosition = Vector3.zero;
                theEffectGo.transform.localScale = Vector3.one;
                theEffectGo.name = theAssetName;
            }
        }

        if (effectNode)
        {
            if(type != EnumBlockTip.EBT_PatternFinished && type != EnumBlockTip.EBT_Unbelievable)
            {
                var theRootTs = effectNode.Find(theAssetName);
                if (theRootTs != null && false == string.IsNullOrEmpty(iconName))
                {
                    var theTs = theRootTs.Find("root/UIImg");
                    if (theTs == null) return null;
                    var theImage = theTs.GetComponent<Image>();
                    var atlaspath = Const_Common.GameAtlas;
                    CoreUtils.SetImg(theImage, atlaspath, iconName);
                    theTs.gameObject.SetActive(true);
                }
            }

            effectNode.gameObject.SetActive(true);
            effectNode.SetParent(curTipPlayNode);
            effectNode.localPosition = Vector3.zero;           
        }

        return effectNode;
    }



    Transform curMaskNode = null;
    void ShowBombFrameEffect(Vector3 targetPos, bool show)
    {
        var maskNode = curGridContainer.Find("UINode_Mask");
        if (maskNode == null) return;

        var theBombBoxNode = maskNode.Find(bombFrameAsset);

        if (show)
        {
            maskNode.gameObject.SetActive(true);
            if (theBombBoxNode == null)
            {
                var theAsset = GameGlobal.GetMgr<ResMgr>().GetRes<GameObject>(bombFrameAsset).GetInstance(curGridContainer.gameObject);
                if (theAsset != null)
                {
                    var theEffectGo = GameObject.Instantiate(theAsset, maskNode);
                    theEffectGo.name = bombFrameAsset;
                    theEffectGo.transform.localScale = Vector3.one;
                    theEffectGo.transform.localPosition = Vector3.zero;
                    theEffectGo.gameObject.SetActive(true);
                    theBombBoxNode = theEffectGo.transform;
                }
            }

            if (theBombBoxNode != null)
            {
                theBombBoxNode.gameObject.SetActive(true);
                theBombBoxNode.transform.localPosition = targetPos;
            }
        }
        else
        {
            maskNode.gameObject.SetActive(false);
            theBombBoxNode?.gameObject.SetActive(false);
        }

        
    }



    List<UnityEngine.Vector3> tempRowPos = new List<UnityEngine.Vector3>();
    List<UnityEngine.Vector3> tempColPos = new List<UnityEngine.Vector3>();
    List<Transform> tempMatchPool = new List<Transform>();
    List<Transform> tempTryMatchPool = new List<Transform>();

    private CoroutineHandler _putDownMatchCoroutine;
    Transform GetMatchClearEffectNode(EnumGridColor color, bool isRow)
    {
        var theColor = color;
        var theName = theColor.ToString();
        var nodeName = $"{theName}_NC";
        var theTs = curBlockPool;
        var effectNode = theTs.Find(nodeName);
        if (effectNode == null)
        {
            var theArray = theName.Split('_');
            if (theArray.Length != 2)
            {
                CLog.Error($"GetEffectNode 出错，{theName} 名字格式不对");
                return null;
            }
            var theGo = theTs.transform.CreateChild(nodeName);
            effectNode = theGo.transform;
            var tempStr = theArray[1];
            tempStr = tempStr.ToLower();
            var theAssetName = $"VFX_row_cancellation_{tempStr}";
            var theAsset = GameGlobal.GetMgr<ResMgr>().GetRes<GameObject>(theAssetName).GetInstance(theGo);
            if (theAsset == null)
            {
                effectNode.parent = null;
                effectNode = null;
                //CLog.Error($"ShowPutDownEffect 出错，{theAssetName}加载失败");
            }
            else
            {
                var theEffectGo = GameObject.Instantiate(theAsset, effectNode);
                theEffectGo.transform.localPosition = Vector3.zero;
            }
        }

        return effectNode;
    }

    void ClearTempMatchPool(int clearCount)
    {
        var theCount = tempMatchPool.Count;
        if (theCount > 0)
        {
            bool bClearAll = clearCount == -1 || clearCount >= theCount;
            if (bClearAll)
            {
                foreach (var item in tempMatchPool)
                {
                    if (item == null) continue;
                    item.SetParent(curBlockPool);
                    item.gameObject.SetActive(false);
                }
                tempMatchPool.Clear();
            }
            else
            {
                for (int i = 0; i < clearCount; i++)
                {
                    var item = tempMatchPool[i];
                    if (item == null) continue;
                    item.SetParent(curBlockPool);
                    item.gameObject.SetActive(false);
                }
            }
            Debug.LogWarning($"ClearTempMatchPool,{clearCount}-{theCount}");
        }
    }

    void ShowMatchClearEffect(EnumGridColor color)
    {
        Transform effectNode = null;
        foreach (var pos in tempRowPos)
        {
            effectNode = GetMatchClearEffectNode(color, true);
            if (effectNode)
            {
                effectNode.SetParent(curGridContainer);
                effectNode.transform.localPosition = pos;
                effectNode.transform.localEulerAngles = Vector3.zero;
                effectNode.gameObject.SetActive(true);
                tempMatchPool.Add(effectNode);
            }
        }

        foreach (var pos in tempColPos)
        {
            effectNode = GetMatchClearEffectNode(color, false);
            if (effectNode)
            {
                effectNode.transform.SetParent(curGridContainer);
                effectNode.transform.localPosition = pos;
                effectNode.transform.localEulerAngles = new Vector3(0f, 0f, 90f);
                effectNode.gameObject.SetActive(true);
                tempMatchPool.Add(effectNode);
            }
        }

        var tempCount = tempMatchPool.Count;
        if (tempCount > 0)
        {
            if (_putDownMatchCoroutine != null) GameGlobal.GetMod<ModCoroutine>().StopCoroutine(_putDownMatchCoroutine);
            _putDownMatchCoroutine = GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(1,
          () =>
          {
              _putDownMatchCoroutine = null;
              ClearTempMatchPool(tempCount);
          }));
        }
    }

    Transform GetTryMatchEffectNode(EnumGridColor color, bool isRow)
    {
        var theColor = color;
        var theName = theColor.ToString();
        var nodeName = $"{theName}_TryMatch";
        var theTs = curBlockPool;
        var effectNode = theTs.Find(nodeName);
        if (effectNode == null)
        {
            var theArray = theName.Split('_');
            if (theArray.Length != 2)
            {
                CLog.Error($"GetEffectNode 出错，{theName} 名字格式不对");
                return null;
            }
            var theGo = theTs.transform.CreateChild(nodeName);
            effectNode = theGo.transform;
            var tempStr = theArray[1];
            tempStr = tempStr.ToLower();
            var theAssetName = $"VFX_Tip_Clear_{tempStr}";
            var theAsset = GameGlobal.GetMgr<ResMgr>().GetRes<GameObject>(theAssetName).GetInstance(theGo);
            if (theAsset == null)
            {
                effectNode.parent = null;
                effectNode = null;
                //CLog.Error($"ShowPutDownEffect 出错，{theAssetName}加载失败");
            }
            else
            {
                var theEffectGo = GameObject.Instantiate(theAsset, effectNode);
                theEffectGo.transform.localPosition = Vector3.zero;
            }
        }

        return effectNode;
    }

    void ShowTryMatchEffect(EnumGridColor color)
    {
        ClearTempTryMatchPool();

        Transform effectNode = null;
        bool effectShowed = false;
        foreach (var pos in tempRowPos)
        {
            effectNode = GetTryMatchEffectNode(color, true);
            if (effectNode)
            {
                effectNode.SetParent(curGridContainer);
                effectNode.transform.localPosition = pos;
                effectNode.transform.localEulerAngles = Vector3.zero;
                effectNode.gameObject.SetActive(true);
                tempTryMatchPool.Add(effectNode);
                effectShowed = true;
            }
        }

        foreach (var pos in tempColPos)
        {
            effectNode = GetTryMatchEffectNode(color, false);
            if (effectNode)
            {
                effectNode.transform.SetParent(curGridContainer);
                effectNode.transform.localPosition = pos;
                effectNode.transform.localEulerAngles = new Vector3(0f, 0f, 90f);
                effectNode.gameObject.SetActive(true);
                tempTryMatchPool.Add(effectNode);
                effectShowed = true;
            }
        }

        if (effectShowed)
        {
            SDK<IHaptics>.Instance.Haptics(HapticTypes.Selection);
        }
    }

    void ClearTempTryMatchPool()
    {
        var theCount = tempTryMatchPool.Count;
        if (theCount > 0)
        {
            foreach (var item in tempTryMatchPool)
            {
                if (item == null) continue;
                item.SetParent(curBlockPool);
                item.gameObject.SetActive(false);
            }
            tempTryMatchPool.Clear();
            Debug.LogWarning($"ClearTempTryMatchPool,{theCount}");
        }
    }


}