
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using DragonPlus.Config.InGame;
using DragonPlus.Config.SkillSet;
using Framework;
using UnityEngine;
using UserSegmentation;
using static BlockPlayManager;

struct ExperienceFeelInfo
{
    public KeyValuePair<int, int> ScoreRange;
    public int feelType;
}

public partial class ModGame
{
    int mcInterval = 3;//多消间隔
    int csInterval = 3;//清屏间隔
    int sbInterval = 3;//小块间隔

    int limitClearComplextiy = 150; //关卡模式下限制消除出块策略的复杂度值(小于该值则不触发消除出块)

    public int LimitClearComplextiy => limitClearComplextiy;

    private int predictChurnGroup = 1;

    EABTestGroup curABTestInGame = EABTestGroup.Group0;

    private bool curBlockIsNewOrder = false;
    public bool CurBlockIsNewOrder => curBlockIsNewOrder;

    private bool curBlockProductIsNative = true;
    private bool curABtestEndlessSimply = false;
    private bool abTestEndlessDifficultTiming = false;

    #region 无尽剧本模式相关声明

    //是否开启无尽剧本模式
    private bool abTestEndlessStory = false;
    // 上一轮技能缓存ID
    private int lastSkillId = 0;
    public int ComplexityGroup { get; private set; }

    #endregion
    public bool CurBlockProductIsNative => curBlockProductIsNative;
    public bool TriggerMClearBlock
    {
        get
        {
            if (theListForClearScreen.Count > 0) return false;
            if (lastMClearRound <= 0) return true;           
            return (genRoundIndex + 1) - lastMClearRound > mcInterval;
        }
    }

    public bool TriggerClearScreenBlock
    {
        get
        {           
            if (lastClearScreenRound <= 0) return true;           
            return (genRoundIndex + 1) - lastClearScreenRound > csInterval; 
        }
    }

    public bool FilterMode1X1BlockForClear
    {
        get
        {
            if (lastClearHave1X1Round <= 0) return true;          
            return (genRoundIndex + 1) - lastClearHave1X1Round <= sbInterval;
        }
    }
   
    public void ChangeBlockProductPlan()
    {
        curBlockProductIsNative = !curBlockProductIsNative;
        CLog.Error($"ChangeBlockProductPlan--curBlockProductIsNative:{curBlockProductIsNative}");
    }

    /// <summary>
    /// 根据Block放置信息获取中心点的UI放置位置
    /// </summary>
    /// <param name="putInfo"></param>
    /// <returns></returns>
    public Vector2 GetBoardUIPosByPutInfo(BlockPutInfo putInfo)
    {
        if (putInfo.blockConfig == 0) return Vector2.zero;
        var tempBlockInfo = GetBlockMatchMatrix(putInfo.blockConfig);
        if (tempBlockInfo.curLayout == 0) return Vector2.zero;

        Vector2Int targetPos = putInfo.targetPos;
        var theMidY = tempBlockInfo.rowNum / 2;
        targetPos.y += theMidY;
        var theMidX = tempBlockInfo.colNum / 2;
        targetPos.x += theMidX;
        var index = targetPos.y * boardHeight + targetPos.x;
        var theGridInfo = grids[index];
        var yOffset = 0f;
        if (tempBlockInfo.rowNum % 2 == 0)
        {
            yOffset = Grid.HalfGridHeight;
        }
        var xOffset = 0f;
        if (tempBlockInfo.colNum % 2 == 0)
        {
            xOffset = -Grid.HalfGridHeight;
        }

        var theTargetPos = theGridInfo.RectTf.localPosition;
        theTargetPos.x += xOffset;
        theTargetPos.y += yOffset;
        return theTargetPos;
    }

    public void FillBestRandomForEdgeDown(List<int> bestRandomForEdgeDown, bool isForBuildUp = false)
    {
        if (bestRandomForEdgeDown == null) return;
        bestRandomForEdgeDown.Clear();
        bestRandomForEdgeDown.Add(0);
        bestRandomForEdgeDown.Add(1);
        bestRandomForEdgeDown.Add(0);
        var theConfig = InGameConfigManager.GetOptimalConfigurationConfig(curScore, predictChurnGroup);
        if (theConfig != null)
        {
            CLog.Info($"FillBestRandomForEdgeDown, configId:{theConfig.Id}, isForBuildUp:{isForBuildUp} ");
            if (isForBuildUp)
            {
                var theSum = theConfig.FirstBuildWeight + theConfig.SecondBuildWeight + 1;
                for (int i = 0; i < bestRandomForEdgeDown.Count; i++)
                {
                    var theRandom = CollectionExtension.sysRandom.Next(1, theSum);
                    var curSum = theConfig.FirstBuildWeight;
                    if (theRandom <= curSum)
                    {
                        bestRandomForEdgeDown[i] = 0;
                    }
                    else
                    {
                        curSum += theConfig.SecondBuildWeight;
                        if(theRandom <= curSum)
                        {
                            bestRandomForEdgeDown[i] = 1;
                        }
                    }
                    CLog.Info($"FillBestRandomForEdgeDown, index:{i}, theRandom:{theRandom}");
                }
            }
            else
            {
                var theSum = theConfig.FirstEliminationWeight + theConfig.SecondEliminationWeight + 1;
                for (int i = 0; i < bestRandomForEdgeDown.Count; i++)
                {
                    var theRandom = CollectionExtension.sysRandom.Next(1, theSum);
                    var curSum = theConfig.FirstEliminationWeight;
                    if (theRandom <= curSum)
                    {
                        bestRandomForEdgeDown[i] = 0;
                    }
                    else
                    {
                        curSum += theConfig.SecondEliminationWeight;
                        if (theRandom <= curSum)
                        {
                            bestRandomForEdgeDown[i] = 1;
                        }
                    }
                    CLog.Info($"FillBestRandomForEdgeDown, index:{i}, theRandom:{theRandom}");
                }
            }
        }
    }

    public void FillBlockPosToBlockListDic(Dictionary<int, List<int>> blockPosToBlockListDic, EnumBlockProductType pType, bool haveMClear = false)
    {
        if (blockPosToBlockListDic == null) return;

        for (var i = 0; i < ModGame.genBlockCount; i++)
        {
            var theKey = i;
            if (haveMClear)
            {
                theKey = i + 1;
                if (theKey >= genBlockCount) break;
            }

            if(!blockPosToBlockListDic.ContainsKey(theKey))
            {
                blockPosToBlockListDic[theKey] = new List<int>();
            }
            else
            {
                blockPosToBlockListDic[theKey].Clear();
            }

            var theList = RefreshBlockProductConfig(pType);
            blockPosToBlockListDic[theKey].AddRange(theList);
        }
    }

    public List<int> RefreshBlockProductConfig(EnumBlockProductType pType)
    {
        if (CurGameType == EnumBlockGameType.BlockGame_Stage)
        {
            curBlockConfig.Clear();
            var theLevelConfig = InGameConfigManager.GetInGameLevelCfgByLevelId(curLevelId);
            if (theLevelConfig != null)
            {
                var theBlockGroupId = theLevelConfig.BlockGroupId;
                var theBlockGroupInfo = InGameConfigManager.GetBlcokLevelGroupConfig(curScore, theBlockGroupId);
                if (theBlockGroupInfo != null && theBlockGroupInfo.BlockList?.Count > 0)
                {
                    curBlockConfig.AddRange(theBlockGroupInfo.BlockList);
                }
            }

            if(curBlockConfig.Count <= 0)
            {
                curBlockConfig.AddRange(blockConfig);
            }

            curBlockConfig.Shuffle();
        }
        else
        {
            bool bWeightHandle = false;
            curBlockConfig.Clear();
            var theBlockGroupInfo = InGameConfigManager.GetBlcokGroupConfig(curScore);
            if (theBlockGroupInfo != null && theBlockGroupInfo.BlockList?.Count > 0)
            {
                curBlockConfig.AddRange(theBlockGroupInfo.BlockList);
                List<int> theWeightList = null;
                switch (pType)
                {                                      
                    case EnumBlockProductType.EBPT_ComplexityUp:
                        theWeightList = theBlockGroupInfo.ComplextiyUpBlockWeight;
                        break;
                    case EnumBlockProductType.EBPT_ComplexityDown:
                        theWeightList = theBlockGroupInfo.ComplextiyDownBlockWeight;
                        break;
                    case EnumBlockProductType.EBPT_Normal:
                        theWeightList = theBlockGroupInfo.RandomBlockWeight;
                        break;
                    default:
                        break;
                }

                if (theWeightList != null && theWeightList.Count == curBlockConfig.Count)
                {
                    CollectionExtension.Shuffle(curBlockConfig, theWeightList);
                    bWeightHandle = true;
                }
            }

            if (curBlockConfig.Count <= 0) curBlockConfig.AddRange(blockConfig);
            if (false == bWeightHandle) CurBlockConfig.Shuffle();
        }
        return curBlockConfig;
    }

    public async Task<bool> HandleBaseBoard(CancellationToken token, int maxComplex, Vector2Int sparsityRange, Vector2Int rowColRange)
    {
        var theTask = BlockPlayManager.Instance.GenerateBlockForBaseBoard(token, maxComplex,
            sparsityRange, rowColRange);
        await theTask;
        
        var theList = theTask.Result;
        var theCount = theList.Count;

        if (theCount > 0)
        {
            foreach (var item in theList)
            {
                var theBlockInfo = GetBlockMatchMatrix(item.blockConfig);
                BlockPlayManager.Instance.HandlePlaceBlock(theBlockInfo, item.targetPos);
                
            }

            var theColor = UnityEngine.Random.Range((int)EnumGridColor.EGC_Red, (int)EnumGridColor.EGC_End + 1);
            var theGridCount = grids.Length;
            for (int index = 0; index < theGridCount; index++)
            {
                if (!BlockPlayManager.Instance.HaveBlock(index)) continue;
                var theGridInfo = grids[index];
                theGridInfo.ShowState = EnumGridState.EGS_Show;
                theGridInfo.ColorState = (EnumGridColor)theColor;
                HandleBoardLayout(index, true);
            }

            return true;
        }

        return false;
    }


    #region Native 出块相关

    async Task<EnumBlockProductType> GenerateBlockConfigsInnerNative(EnumBlockProductType pType, int milliSecond = 1000, bool isAuto = false, List<BlockPutInfo> listForClearScreen = null)
    {
        var realType = pType;

        switch (pType)
        {
            case EnumBlockProductType.EBPT_Revive:
                {
                    await GenerateBlockConfigsForRevive(milliSecond);
                    realType = EnumBlockProductType.EBPT_Revive;
                    break;
                }
            case EnumBlockProductType.EBPT_ClearScreen:
                {
                    if (isAuto == false) genList.Clear();
                    realType = GenerateBlockConfigsFromNativeForClearScreen(isAuto, listForClearScreen);
                    break;
                }
            case EnumBlockProductType.EBPT_Normal:
            case EnumBlockProductType.EBPT_ComplexityUp:
            case EnumBlockProductType.EBPT_ComplexityDown:
            case EnumBlockProductType.EBPT_Hard:
            case EnumBlockProductType.EBPT_MultiClear:
                {
                    genList.Clear();
                    var theNativeType = GetNativeTypeByProductType(pType);
                    realType = GenerateBlockConfigsFromNative(theNativeType);
                    break;
                }
            default:
                break;
        }
        return realType;
    }

    private void ModifyTypeAndBlockGroup(ref BlockMatchCoreNative.StrategyEnum eType, ref ulong blockGroupId)
    {
        if (abTestEndlessStory)
        {
            var skillId = UserSegmentationManager.Instance.GetCurrentSkillId();
            var skillStoryConfig = SkillSetConfigManager.GetSkillConfigInfo(skillId);
            if (lastSkillId == skillId)
            {
                skillId = skillStoryConfig.FallbackSkillId;
                skillStoryConfig = SkillSetConfigManager.GetSkillConfigInfo(skillId);
            }
#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (GM_NextSkillId > 0)
            {
                var skillConfigTemp = SkillSetConfigManager.GetSkillConfigInfo(GM_NextSkillId);
                if (skillConfigTemp != null)
                {
                    skillStoryConfig = skillConfigTemp;
                    CLog.Info($"<color=#FF0000> [无尽剧本] 通过GM命令设置 skillId [{GM_NextSkillId}] </color>");
                }
                GM_NextSkillId = 0;
            }
#endif
            if (skillStoryConfig != null)
            {
                if (Enum.TryParse<EnumBlockProductType>(skillStoryConfig.ActionEnum,
                        out var enumValue))
                {
                    eType = GetNativeTypeByProductType(enumValue);
                    blockGroupId = (ulong)skillStoryConfig.BlockGroupId;
                        
                    CLog.Info($"<color=#00FF33>[无尽剧本] 替换后的技能ID 为{skillId}  Native出块类型为 {eType.ToString()} 方块组ID为 {blockGroupId}</color>");
                }
                lastSkillId = skillId;
            }
            else
            {
                CLog.Error($" [无尽剧本] 未找到对应的技能配置 skillId [{skillId}]");
            }
        }
    }

    EnumBlockProductType GenerateBlockConfigsFromNativeForClearScreen(bool isAuto, List<BlockPutInfo> listForClearScreen = null)
    {
        var eType = BlockMatchCoreNative.StrategyEnum.StrategyClearScreen;
        var theRealType = EnumBlockProductType.EBPT_Guarantee;
        var theBoard = curGridLayout;
        var theMask1 = curGridLayoutMask1;
        var theMask2 = curGridLayoutMask2;
        var theMask3 = curGridLayoutMask3;
        var theMask4 = curGridLayoutMask4;
        var theMask5 = curGridLayoutMask5;
        var theMask6 = curGridLayoutMask6;
        var complexity = (int)BlockPlayManager.Instance.Complexity;
        var historyHighest = StorageExtension.GameEndlssStorage.CurFirstScore;
        
        // 如果historyHighest为0，根据当前关卡等级设置默认值
        if (historyHighest == 0)
        {
            if (curLevelId <= 30)
            {
                historyHighest = 1500;
            }
            else if (curLevelId <= 60)
            {
                historyHighest = 3500;
            }
            else
            {
                historyHighest = 6500;
            }
        }
        
        // 设置游戏模式：无尽模式为0，关卡模式为1
        ulong gameMode = 0;
        ulong blockGroupId = 0UL;
        if (CurGameType == EnumBlockGameType.BlockGame_Stage)
        {
            gameMode = 1;
        }
        // else // 无尽模式下需要判断技能剧本
        // {
        //     ModifyTypeAndBlockGroup(ref eType, ref blockGroupId);
        // }
        
        ulong[] ids = BoardRecommendShapes(theBoard, 
            theMask1, theMask2, theMask3, 
            theMask4, theMask5, theMask6, 
            gameMode, (ulong)complexity, (ulong)curScore, (ulong)historyHighest, 
            eType ,blockGroupId);
        var tempType = (BlockMatchCoreNative.StrategyEnum)ids[^1];

        if (isAuto == false || tempType == BlockMatchCoreNative.StrategyEnum.StrategyClearScreen)
        {
            if (listForClearScreen != null)
            {
                for (int i = 0; i < genBlockCount; i++)
                {
                    int theId = (int)ids[i];
                    if (theId > 0)
                    {
                        var thePos = InvaildPos;
                        var thePosIndex = genBlockCount + i;
                        if(thePosIndex < ids.Length) thePos = ParsePos(ids[genBlockCount + i]);

                        listForClearScreen.Add(new BlockPutInfo()
                        {
                            blockConfig = theId,
                            targetPos = thePos,
                        });
                    }
                }
            }
            else
            {
                genList.Clear();
                for (int i = 0; i < genBlockCount; i++)
                {
                    int theId = (int)ids[i];
                    if (theId > 0)
                    {
                        genList.Add(theId);
                    }
                    else
                    {
                        var theConfig = blockConfig.GetRandomValue();
                        genList.Add(theConfig);
                    }
                }
            }
        }

        theRealType = GetProductTypeByNativeType(tempType);

        CLog.Info($"GenerateBlockConfigsFromNativeForClearScreen--[{eType.ToString()}->{tempType.ToString()}],[{ids[0]},{ids[1]},{ids[2]}]");
        return theRealType;
    }

    EnumBlockProductType GenerateBlockConfigsFromNative(BlockMatchCoreNative.StrategyEnum eType)
    {
        var theRealType = EnumBlockProductType.EBPT_Guarantee;
        var theBoard = curGridLayout;
        var theMask1 = curGridLayoutMask1;
        var theMask2 = curGridLayoutMask2;
        var theMask3 = curGridLayoutMask3;
        var theMask4 = curGridLayoutMask4;
        var theMask5 = curGridLayoutMask5;
        var theMask6 = curGridLayoutMask6;
        var complexity = (int)BlockPlayManager.Instance.Complexity;
        var historyHighest = StorageExtension.GameEndlssStorage.CurFirstScore;
        
        // 如果historyHighest为0，根据当前关卡等级设置默认值
        if (historyHighest == 0)
        {
            if (curLevelId <= 30)
            {
                historyHighest = 1500;
            }
            else if (curLevelId <= 60)
            {
                historyHighest = 3500;
            }
            else
            {
                historyHighest = 6500;
            }
        }
        
        // 设置游戏模式：无尽模式为0，关卡模式为1
        ulong gameMode = 0;
        ulong blockGroupId = 0UL;
        if (CurGameType == EnumBlockGameType.BlockGame_Stage)
        {
            gameMode = 1;
        }
        else // 无尽模式下需要判断技能剧本
        {
            ModifyTypeAndBlockGroup(ref eType, ref blockGroupId);
        }

        ulong[] ids = BoardRecommendShapes(theBoard,
            theMask1, theMask2, theMask3,
            theMask4, theMask5, theMask6,
            gameMode, (ulong)complexity, (ulong)curScore, (ulong)historyHighest,
            eType, blockGroupId);

        var tempCount = GenerateBlockCount;
        for (int i = 0; i < tempCount; i++)
        {
            int theId = (int)ids[i];
            if (theId > 0)
            {
                genList.Add((int)theId);
            }
            else
            {
                var theConfig = blockConfig.GetRandomValue();
                genList.Add(theConfig);
            }
        }

        var tempType = (BlockMatchCoreNative.StrategyEnum)ids[^1];
        if (eType != tempType)
        {
            CLog.Info($"<color=red>去程[{eType.ToString()}]返程[{tempType.ToString()}]类型不一致，需要重新随机</color>");
            if (CurGameType == EnumBlockGameType.BlockGame_Endless)
            {
                ModifyTypeAndBlockGroup(ref eType, ref blockGroupId);
            }
            ids = BoardRecommendShapes(theBoard,
                theMask1, theMask2, theMask3,
                theMask4, theMask5, theMask6,
                gameMode, (ulong)complexity, (ulong)curScore, (ulong)historyHighest,
                eType, blockGroupId);

            for (int i = 0; i < tempCount; i++)
            {
                int theId = (int)ids[i];
                if (theId > 0)
                {
                    genList.Add((int)theId);
                }
                else
                {
                    var theConfig = blockConfig.GetRandomValue();
                    genList.Add(theConfig);
                }
            }
        }
        theRealType = GetProductTypeByNativeType(tempType);
        if(theListForClearScreen.Count > 0)
        {
            if (theRealType == EnumBlockProductType.EBPT_Hard) theRealType = EnumBlockProductType.EBPT_ClearScreen;
        }

        CLog.Info($"GenerateBlockConfigsFromNative--[{eType.ToString()}->{tempType.ToString()}],[{ids[0]},{ids[1]},{ids[2]}]");
        return theRealType;
    }


    private ulong[] BoardRecommendShapes(ulong board, ulong mask1, ulong mask2, ulong mask3,ulong mask4,ulong mask5,ulong mask6, ulong gameMode, ulong complexity ,ulong score, ulong historyHighest, BlockMatchCoreNative.StrategyEnum strategy,ulong blockGroupId = 0UL)
    {
        var tempArray = BlockMatchCoreNative.BoardRecommendShapes(board, mask1, mask2, mask3, mask4, mask5, mask6, gameMode, complexity, score, historyHighest,0,0,0,(ulong)strategy, blockGroupId);
        CLog.Info($"BlockMatchCoreNative:{board}:{mask1}:{mask2}:{mask3} 2x2方块:{mask4}:{mask5}:{mask6} 分数:{score} 复杂度:{complexity} 历史最高分:{historyHighest} 请求：{strategy.ToString()} 实际：{((BlockMatchCoreNative.StrategyEnum)tempArray[^1]).ToString()}   id1:{tempArray[0]}:{ParsePos(tempArray[3]).ToString()}  id2:{tempArray[1]}:{ParsePos(tempArray[4]).ToString()} id3:{tempArray[2]}:{ParsePos(tempArray[5]).ToString()}   ");
        return tempArray;
    }




    #endregion


    #region 原始出块相关

    async Task GenerateBlockConfigsInnerOrigin(EnumBlockProductType pType, int milliSecond = 1000, bool isLevelDown = false, List<BlockPutInfo> listForClearScreen = null, int curMaxDeepForClearScreen = -1)
    {
        switch (pType)
        {
            case EnumBlockProductType.EBPT_Revive:
                {
                    await GenerateBlockConfigsForRevive(milliSecond);
                    break;
                }
            case EnumBlockProductType.EBPT_Normal:
                {
                    await GenerateBlockConfigsForRandom(milliSecond, isLevelDown);
                    break;
                }
            case EnumBlockProductType.EBPT_Hard:
                {
                    await GenerateBlockConfigsForHard(milliSecond);
                    break;
                }
            case EnumBlockProductType.EBPT_ComplexityUp:
                {
                    await GenerateBlockConfigsForBuild(milliSecond);
                    break;
                }
            case EnumBlockProductType.EBPT_ComplexityDown:
                {
                    await GenerateBlockConfigsForClear(milliSecond);
                    break;
                }
            case EnumBlockProductType.EBPT_MultiClear:
                {
                    await GenerateBlockConfigsForMultiClear(milliSecond);
                    break;
                }
            case EnumBlockProductType.EBPT_ClearScreen:
                {
                    await GenerateBlockConfigsForClearScreen(milliSecond, listForClearScreen, curMaxDeepForClearScreen);
                    break;
                }
            default:
                break;
        }
    }


    async Task GenerateBlockConfigsForRandom(int milliSecond, bool isLevelDown)
    {
        CancellationTokenSource cts = new CancellationTokenSource();
        cts.CancelAfter(milliSecond);
        var theFilterList = genRoundIndex == 0 ? firstRoundFilter : null;
        if (theFilterList == null && isLevelDown == false) theFilterList = randomFilter;

        var theTask = BlockPlayManager.Instance.GenerateBlockForRandom(theFilterList, cts.Token, isLevelDown);
        await theTask;
        cts.Cancel();

        var theInfo = theTask.Result;
        theMClearBlockId = theInfo.blockConfig;
    }

    async Task GenerateBlockConfigsForHard(int milliSecond)
    {
        if (theListForClearScreen.Count > 0)
        {
            ModGame.GenList.Clear();
            List<int> theList = RefreshBlockProductConfig(EnumBlockProductType.EBPT_Hard);
            var theCount = GenerateBlockCount;
            for (int i = 0; i < theCount; i++)
            {
                var blockId = theList.GetRandomValue();
                ModGame.GenList.Add(blockId);
            }
        }
        else
        {
            CancellationTokenSource cts = new CancellationTokenSource();
            cts.CancelAfter(milliSecond);
            var theTask = BlockPlayManager.Instance.GenerateBlockForHard(cts.Token);
            await theTask;
            cts.Cancel();
        }
    }

    EnumBuildUpType buildUpType = EnumBuildUpType.EBT_None;
    void HandleBuildUpType(List<int> weights = null)
    {
        buildUpType = EnumBuildUpType.EBT_EdgeDown;
        var theCount = weights != null ? weights.Count : 0;
        if (theCount == 2)
        {
            var theSum = weights.Sum();
            var theRandom = UnityEngine.Random.Range(1, theSum + 1);
            if (theRandom > weights[0])
            {
                buildUpType = EnumBuildUpType.EBT_ComplexityUp;
            }
            CLog.Info($"GenerateBlockConfigsForBuild---theRandom:{theRandom}");
        }
        else
        {
            var tempConfig = InGameConfigManager.GetComplextiyUpAlgorithmConfig(curScore, predictChurnGroup);
            if (tempConfig != null)
            {
                var theSum = tempConfig.ComplexityUpWeight + tempConfig.EdgeDownWeight + tempConfig.EdgeSecondDownWeight + 1;
                var theRandom = CollectionExtension.sysRandom.Next(1, theSum);
                var theCurSum = tempConfig.ComplexityUpWeight;
                if (theRandom > 0 && theRandom <= theCurSum)
                {
                    buildUpType = EnumBuildUpType.EBT_ComplexityUp;
                }
                else
                {
                    theCurSum += tempConfig.EdgeDownWeight;
                    if (theRandom <= theCurSum)
                    {
                        buildUpType = EnumBuildUpType.EBT_EdgeDown;
                    }
                    else
                    {
                        theCurSum += tempConfig.EdgeSecondDownWeight;
                        if (theRandom <= theCurSum)
                        {
                            buildUpType = EnumBuildUpType.EBT_EdgeDown_BestRandom;
                        }
                    }
                }
                
                CLog.Info($"GenerateBlockConfigsForBuild---configId:{tempConfig.Id},theRandom:{theRandom}");
            }
        }
    }

    async Task GenerateBlockConfigsForBuild(int milliSecond)
    {
        var theType = buildUpType == EnumBuildUpType.EBT_None ? EnumBuildUpType.EBT_EdgeDown : buildUpType;
        int theComplex = (int)BlockPlayManager.Instance.Complexity;
        var theFilterList = InGameConfigManager.GetFilterBlockList(theComplex);
        CancellationTokenSource cts = new CancellationTokenSource();
        cts.CancelAfter(milliSecond);
        var theTask = BlockPlayManager.Instance.GenerateBlockForBuildUp(theType, theFilterList, cts.Token);
        await theTask;
        cts.Cancel();

        var theInfo = theTask.Result;
        theMClearBlockId = theInfo.blockConfig;
    }

    EnumClearType clearBlockType = EnumClearType.ECT_None;
    void HandleClearBlockType(List<int> weights = null)
    {
        clearBlockType = EnumClearType.ECT_EdgeDown;
        var theCount = weights != null ? weights.Count : 0;
        if (theCount == 2)
        {
            var theSum = weights.Sum();
            var theRandom = UnityEngine.Random.Range(1, theSum + 1);
            if (theRandom > weights[0])
            {
                clearBlockType = EnumClearType.ECT_ClearGrid;
            }
            CLog.Info($"GenerateBlockConfigsForClear---theRandom:{theRandom}");
        }
        else
        {
            var tempConfig = InGameConfigManager.GetComplextiyDownAlgorithmConfig(curScore, predictChurnGroup);
            if (tempConfig != null)
            {
                var theSum = tempConfig.EliminateWeight + tempConfig.EdgeDownWeight + tempConfig.EdgeSecondDownWeight + 1;
                var theRandom = CollectionExtension.sysRandom.Next(1, theSum);
                var theCurSum = tempConfig.EliminateWeight;
                if (theRandom > 0 && theRandom <= theCurSum)
                {
                    clearBlockType = EnumClearType.ECT_ClearGrid;
                }
                else
                {
                    theCurSum += tempConfig.EdgeDownWeight;
                    if (theRandom <= theCurSum)
                    {
                        clearBlockType = EnumClearType.ECT_EdgeDown;
                    }
                    else
                    {
                        theCurSum += tempConfig.EdgeSecondDownWeight;
                        if (theRandom <= theCurSum)
                        {
                            clearBlockType = EnumClearType.ECT_EdgeDown_BestRandom;
                        }
                    }
                }
                
                CLog.Info($"GenerateBlockConfigsForClear--configId:{tempConfig.Id},theRandom:{theRandom}");
            }
        }
    }

    async Task GenerateBlockConfigsForClear(int milliSecond)
    {
        EnumClearType theType = clearBlockType == EnumClearType.ECT_None ? EnumClearType.ECT_EdgeDown : clearBlockType;
        CancellationTokenSource cts = new CancellationTokenSource();
        cts.CancelAfter(milliSecond);
        var theTask = BlockPlayManager.Instance.GenerateBlockForClear(theType, cts.Token);
        await theTask;
        cts.Cancel();
    }

    async Task GenerateBlockConfigsForClearScreen(int milliSecond, List<BlockPutInfo> listForClearScreen = null, int curMaxDeep = -1)
    {
        int theTime = milliSecond;
        if (listForClearScreen != null) theTime = 100;
        CancellationTokenSource cts = new CancellationTokenSource();
        cts.CancelAfter(theTime);
        var theTask = BlockPlayManager.Instance.GenerateBlockForClearScreen(cts.Token, curMaxDeep);
        await theTask;
        cts.Cancel();

        var theList = theTask.Result;
        var theCount = theList.Count;

        if (theCount > 0)
        {
            if (listForClearScreen != null)
            {
                listForClearScreen.AddRange(theList);
            }
            else
            {
                genList.Clear();
                for (int i = 0; i < genBlockCount; i++)
                {
                    if (i < theCount && theList[i].blockConfig > 0)
                    {
                        genList.Add(theList[i].blockConfig);
                    }
                    else
                    {
                        var theConfig = blockConfig.GetRandomValue();
                        genList.Add(theConfig);
                    }
                }
            }
        }
    }


    async Task GenerateBlockConfigsForMultiClear(int milliSecond)
    {
        CancellationTokenSource cts = new CancellationTokenSource();
        cts.CancelAfter(milliSecond);
        var theTask = BlockPlayManager.Instance.GenerateBlockForMultiClear(cts.Token);
        await theTask;
        cts.Cancel();

        var theRes = theTask.Result;
        genList.Clear();
        if (theRes.blockId > 0)
        {
            for (int i = 0; i < genBlockCount; i++)
            {
                if (i == 0)
                {
                    genList.Add(theRes.blockId);
                }
                else
                {
                    var theConfig = blockConfig.GetRandomValue();
                    genList.Add(theConfig);
                }
            }
        }
    }



    async Task GenerateBlockConfigsForRevive(int milliSecond)
    {
        genList.Clear();
        var tempCount = GenerateBlockCount;
        for (int i = 0; i < tempCount; i++)
        {
            var theEle = -1;
            if (i == 0)
            {
                CancellationTokenSource cts = new CancellationTokenSource();
                cts.CancelAfter(milliSecond);
                var theTask = GenerateBestBlock(cts.Token);
                await theTask;
                theEle = theBestConfig;
                if (theEle == -1)
                {
                    theEle = Block1x1Config;
                }
            }
            else
            {//临时的复活需求
                theEle = Block1x1Config;
            }

            if (theEle == -1)
            {
                theEle = genList.Count > 0 ? curBlockConfig.GetRandomValue(genList)
                : curBlockConfig.GetRandomValue(genList);
            }
            genList.Add(theEle);
        }
    }

    #endregion

}