using DragonPlus.Ad;
using DragonPlus.Config.Global;
using DragonPlus.Core;
using DragonU3DSDK.Network.API.Protocol;
using Framework;
using MergeHotupdate.Game.Events;
using TMGame;
using System;
using System.Collections.Generic;
using UnityEngine.UI;

public class BuyEnergyViewData
{
    public Action onClose;
    public Action onWatchAds;
    public Action onBuyEnergy;
}

/// <summary>
/// 购买体力界面
/// </summary>
public class UIView_BuyEnergy : UIView_BuyEnergyBase
{
    private EnergySys energySys;
    private Image _rvButtonBg;
    BuyEnergyViewData curViewData;

    BuyEnergyViewData CurViewData
    {
        get
        {
            if (ViewData == null) return null;
            return ViewData as BuyEnergyViewData;
        }
    }

    protected override void BindComponent()
    {
        base.BindComponent();
        _rvButtonBg = UIBtn_Rv.transform.Find("BGRV").GetComponent<Image>();
    }

    protected override void OnInit(object viewData)
    {
        base.OnInit(viewData);
        energySys = GameGlobal.GetMod<EnergySys>();
    }

    protected override void RegisterUIEvent()
    {
        base.RegisterUIEvent();
        UIBtn_Close.onClick.AddListener(OnCloseBtn);
        UIBtn_Buy.onClick.AddListener(OnBuyBtn);
        UIBtn_Rv.onClick.AddListener(OnAdButtonClicked);
    }

    protected override void RemoveGameEvent()
    {
        base.RemoveGameEvent();
        UIBtn_Close.onClick.RemoveListener(OnCloseBtn);
        UIBtn_Buy.onClick.RemoveListener(OnBuyBtn);
        UIBtn_Rv.onClick.RemoveListener(OnAdButtonClicked);
    }

    protected override void RegisterGameEvent()
    {
        base.RegisterGameEvent();
        EventBus.Subscribe<EventEnergyChange>(OnEventEnergyChange);
    }

    protected override void OnOpen()
    {
        base.OnOpen();
        
        InitAdBtn();
        RefreshView();
    }

    private void InitAdBtn()
    {        
        UIBtn_Rv.gameObject.gameObject.SetActive(true);
    }

    protected override void OnUpdate()
    {
        base.OnUpdate();
        var newText = SDKUtil.TimeDate.GetTimeString("%mm:%ss", (int)(energySys.LeftAutoAddEnergyTime() * 0.001));
        UITxt_Time.SetText(newText);
    }

    private void RefreshView()
    {
        UITxt_Count.SetText(energySys.GetEnergy().ToString());
        UITxt_NeedCount.SetText(GameGlobal.GetMgr<ConfigMgr>().GetConstConfig<Table_Global_Global, int>("EnergyGemPrice").ToString());
        var rvCount = GameGlobal.GetMod<AdSys>().GetRewardLastCount(eAdReward.BuyEnergy);
        if (rvCount <= 0)
        {
            CoreUtils.SetImg(_rvButtonBg, "CommonAtlas", "bm_common_btn4");
            UIBtn_Rv.interactable  = false;
        }
        
    }

    private void OnBuyBtn()
    {
        Close();
        if (!energySys.BuyEnergyWithCoin())
        {
            GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_ShopMain,
                new ShopOpenParams(false, 0, ShopOpenSource.CoinWidget, ShopPageType.PageCoin));
            return;
        }
     
        EventBus.Dispatch(new EventBuyEnergyInOutLives());
        var theViewData = CurViewData;
        if (theViewData != null) theViewData.onBuyEnergy?.Invoke();
    }

    private void OnCloseBtn()
    {
        Close();
        var theViewData = CurViewData;
        if (theViewData != null) theViewData.onClose?.Invoke();
    }

    private void OnEventEnergyChange(EventEnergyChange evt)
    {
        UITxt_Count.SetText(energySys.GetEnergy().ToString());
        if (energySys.IsEnergyFull())
        {
            Close();
        }
    }

    private void OnAdButtonClicked()
    {
        if (energySys.IsEnergyFull()) return;
        var adSys = GameGlobal.GetMod<AdSys>();
        adSys.TryShowRewardedVideo(eAdReward.BuyEnergy, (result, str) =>
        {
            CLog.Warning($"TryShowRewardedVideo---result is {result}");
            if (result == AdPlayResult.Success)
            {
                energySys.AddEnergy(1, new BIHelper.ItemChangeReasonArgs(BiEventBlockMatch1.Types.ItemChangeReason.Ads)
                {
                    data1 = ((int)eAdReward.BuyEnergy).ToString(),
                },false);
                Close();
                var itemCfg = GameGlobal.GetMgr<ConfigMgr>().GetConfig<Table_Global_Item>((int)EItemType.Energy);
                itemCfg.Amount = 1;
                GameGlobal.GetMod<FlySys>().FlyRewardItems(new List<Table_Global_Item>() { itemCfg }, adGetEnergy:true);
                EventBus.Dispatch(new EventBuyEnergyInOutLives());
                
                var theViewData = CurViewData;
                if (theViewData != null)  theViewData.onWatchAds?.Invoke();
            }
            else
            {
                CLog.Error($"TryShowRewardedVideo---看广告加体力失败, result is {result}");
            }
        });
    }

    protected override void OnClose()
    {
        base.OnClose();
        EventBus.Unsubscribe<EventEnergyChange>(OnEventEnergyChange);
    }
}