using System;
using System.Collections.Generic;
using System.Text;
using DragonPlus.Config.Global;
using DragonPlus.Core;
using DragonPlus.Network;
using DragonPlus.Save;
using DragonU3DSDK.Network.API.Protocol;
using Framework;
using TMGame;
using TMGame.Storage;

/// <summary>
/// ABTest类型
/// </summary>
public enum EABTestType
{
    ABTestGuide = 1,
    ABTestBlockNative = 2,
    ABTestInGame = 3,
    ABTestBlockSorting = 4,
    AbTestBuildCoinTrigger = 5,
    AbTestRandomWeight = 6,
    AbTestDeleteChallenge = 7,
    AbTestBaseBoard = 8,
    ABTestNativeAlgorithm = 9,
    ABTestLevelGroup = 10,
    ABTestBaseBoardNew = 11,
    ABTestPlayGuide = 12,
    ABTestLike = 13,
    ABTestHardExtraScore = 14,
    abTestLevelGroup2 = 15,
    abTestEndlessSimplify = 16,
    abTestEndlessDifficultTiming = 17,
    abTestEndlessCoinRevive = 18,
    abTestAudio = 19,
    abTestEndlessStory = 20,
    abTestLevelGroup3 = 21,
    abTestAdsOpenLevel = 22,
}

/// <summary>
/// ABTest分组类型
/// </summary>
public enum EABTestGroup
{
    Group0 = -1,
    Group1 = 0,
    Group2 = 1,
    Group3 = 2,
    Group4 = 3,
    Group5 = 4,
    Group6 = 5,
    Gruop7 = 6,
    Group8 = 7,
    Group9 = 8,
    Gruop10 = 9,
}

public class ModABTestEx
{
    public static bool IPlayGroupB()
    {
        return false;
        var group = GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.AbTestDeleteChallenge);
        return group == EABTestGroup.Group2;
    }
}

/// <summary>
/// ABTest模块
/// </summary>
public class ModABTest : LogicSys
{
    private const string ClientPrefix = "Client_";
    public const EABTestGroup DefaultGroup = EABTestGroup.Group1;

    private StorageCommon storageCommon;
    private Dictionary<EABTestType, Table_Global_ABTest> abTestType2ABTestCfgCache = new Dictionary<EABTestType, Table_Global_ABTest>();
    private Dictionary<string, Table_Global_ABTest> abTestKey2ABTestCfgCache = new Dictionary<string, Table_Global_ABTest>();

    public override void Init()
    {
        var abTestCfgList = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_Global_ABTest>();
        foreach (var abTestCfg in abTestCfgList)
        {
            abTestType2ABTestCfgCache.TryAdd((EABTestType)abTestCfg.AbTestType, abTestCfg);
            abTestKey2ABTestCfgCache.TryAdd(abTestCfg.AbTestKey, abTestCfg);
        }
    }

    public void OnLoginSuccess()
    {
        storageCommon = SDK<IStorage>.Instance.Get<StorageCommon>();
        RequestABTestConfig();
    }

    /// <summary>
    /// 获取ABTest分组
    /// </summary>
    public EABTestGroup GetABTestGroup(EABTestType abTestType)
    {
        var abTestCfg = GetABTestCfg(abTestType);
        if (abTestCfg == null)
            return EABTestGroup.Group1;

        try
        {
            string serverkey = abTestCfg.AbTestKey;
            string clientKey = $"{ClientPrefix}{serverkey}";
            EABTestGroup abTestGroup;
            if (abTestCfg.UseClient)
            {
                if (storageCommon.Abtests.ContainsKey(clientKey))
                {
                    abTestGroup = (EABTestGroup)(int.Parse(storageCommon.Abtests[clientKey]));
                }
                else
                {
                    abTestGroup = DefaultGroup;
                    if (CheckClientMinAppVersion(abTestType))
                    {
                        SetClientABTestGroupValue(abTestType, abTestGroup);
                    }
                }
            }
            else
            {
                if (storageCommon.Abtests.ContainsKey(serverkey))
                {
                    abTestGroup = (EABTestGroup)(int.Parse(storageCommon.Abtests[serverkey]));
                }
                else
                {
                    abTestGroup = DefaultGroup;
                }
            }
            return abTestGroup;
        }
        catch (Exception e)
        {
            CLog.Error($"获取ABTest分组失败；{abTestType}");
            return DefaultGroup;
        }
    }

    /// <summary>
    /// 设置客户端ABTest分组的值
    /// </summary>
    public void SetClientABTestGroupValue(EABTestType abTestType, EABTestGroup abTestGroup, bool force = false)
    {
        var abTestCfg = GetABTestCfg(abTestType);
        if (abTestCfg == null)
            return;
        string serverkey = abTestCfg.AbTestKey;
        string clientKey = $"{ClientPrefix}{serverkey}";
        if (!storageCommon.Abtests.ContainsKey(clientKey))
        {
            storageCommon.Abtests.Add(clientKey, ((int)abTestGroup).ToString());
        }
        else if (force)
        {
            storageCommon.Abtests[clientKey] = ((int)abTestGroup).ToString();
        }
    }

    /// <summary>
    /// 检查客户端固化分组的最低app版本号
    /// </summary>
    public bool CheckClientMinAppVersion(EABTestType abTestType)
    {
        var abTestCfg = GetABTestCfg(abTestType);
        if (abTestCfg == null)
            return false;
        var storageGlobal = SDK<IStorage>.Instance.Get<StorageGlobal>();
        try
        {
            int fistAppVersionNum = 0;//
            int.TryParse(storageGlobal.UserData.FirstAppVersion.Replace("v", ""),out fistAppVersionNum);
            int minAppVersionNum = 0;
            int.TryParse(abTestCfg.ClientMinAppVersion.Replace("v", ""),out minAppVersionNum);
            var ret = fistAppVersionNum >= minAppVersionNum;
            return ret;
        }
        catch (Exception e)
        {
            CLog.Error("检查客户端固化分组的最低app版本号异常，" + e);
            return false;
        }
        return false;
    }

    public static bool isGetAbData = false;
    public static long RequestWaitTime = 0;
    public void RequestABTestConfig()
    {
        try
        {
            SDK<IRemoteRequest>.Instance.HandleRequest(new CGetABTestConfig(), (SGetABTestConfig response) =>
            {
                CLog.Info($"获取到服务器ABTest分组配置，获取到的个数：{response.AbtestConfig.Count}");

                var storageCommon = SDK<IStorage>.Instance.Get<StorageCommon>();
                foreach (var kv in response.AbtestConfig)
                {
                    if (storageCommon.Abtests.ContainsKey(kv.Key))
                    {
                        storageCommon.Abtests[kv.Key] = kv.Value.Group;
                    }
                    else
                    {
                        storageCommon.Abtests.Add(kv.Key, kv.Value.Group);
                    }
                    // 判断是否使用客户端固化分组
                    string clientKey = $"{ClientPrefix}{kv.Key}";
                    if (abTestKey2ABTestCfgCache.TryGetValue(kv.Key, out var _abTestCfg)
                        && _abTestCfg.UseClient
                        && !storageCommon.Abtests.ContainsKey(clientKey))
                    {
                        bool b = true;
                        // 有些需要额外判断
                        // switch ((EABTestType)_abTestCfg.AbTestType)
                        // {
                        //     case EABTestType.ABTestGuide:
                        //         // if (Game.GetMod<ModFindTM>().GeCurMainLevelIndex() + 1 > 5)
                        //         //     b = false;
                        //         break;
                        // }
                        if (b)
                        {
                            storageCommon.Abtests.Add(clientKey, kv.Value.Group);
                            CLog.Info($"添加ABTest客户端分组存档，{kv.Key} : {kv.Value.Group}");
                        }
                    }
                    CLog.Info($"修改ABTest服务器分组存档，{kv.Key} : {kv.Value.Group}");

                    if (storageCommon.Abtests.ContainsKey(kv.Key)
                        && storageCommon.Abtests.ContainsKey(clientKey)
                        && storageCommon.Abtests[kv.Key] != storageCommon.Abtests[clientKey])
                    {
                       // BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventAbtestGrouperror);
                    }
                }
                isGetAbData = true;
            }, (errorCode, errorMsg, response) =>
            {
                //
                isGetAbData = true;
                CLog.Error("获取到服务器ABTest分组配置失败" + errorMsg);
            });
        }
        catch (Exception e)
        {
            isGetAbData = true;
            CLog.Error("获取到服务器ABTest分组配置异常" + e.Message);
        }
    }

    public Table_Global_ABTest GetABTestCfg(EABTestType abTestType)
    {
        if (abTestType2ABTestCfgCache.TryGetValue(abTestType, out var _cfg))
            return _cfg;
        CLog.Error($"获取不到 [{abTestType}] ABTest类型的配置");
        return null;
    }

    public Table_Global_ABTest GetABTestCfg(string abTestKey)
    {
        if (abTestKey2ABTestCfgCache.TryGetValue(abTestKey, out var _cfg))
            return _cfg;
        CLog.Error($"获取不到 [{abTestKey}] ABTestKey的配置");
        return null;
    }

    public string LogAllABTest()
    {
        StringBuilder sb = new StringBuilder();
        foreach (var kvp in storageCommon.Abtests)
        {
            sb.Append(kvp.Key + ":" + kvp.Value + ",");
        }
        return sb.ToString();
    }
}