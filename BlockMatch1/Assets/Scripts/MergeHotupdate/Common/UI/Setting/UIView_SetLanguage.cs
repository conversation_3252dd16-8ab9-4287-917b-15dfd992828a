using System.Collections.Generic;
using System.Linq;
using DragonPlus.Core;
using TMGame;

public class SetLanguageBtnData
{
    public Language cfg;
}

/// <summary>
/// 设置语言界面
/// </summary>
public class UIView_SetLanguage : UIView_SetLanguageBase
{
    protected override void RegisterGameEvent()
    {
        base.RegisterGameEvent();
        EventBus.Subscribe<EventLanguageChange>(OnEventLanguageChange);
    }

    protected override void RegisterUIEvent()
    {
        base.RegisterUIEvent();
        UIBtn_Close.onClick.AddListener(OnCloseBtn);
    }

    protected override void OnOpen()
    {
        base.OnOpen();
        var languageList = GameGlobal.GetMod<LanguageModel>().GetCfg();
        var list = languageList.Values.ToList();
        List<SetLanguageBtnData> setLanguageBtnDatas = new List<SetLanguageBtnData>();
        for (int i = 0; i < list.Count; i++)
        {
            if (list[i].IsEnabled)
            {
                SetLanguageBtnData data = new SetLanguageBtnData();
                data.cfg = list[i];
                setLanguageBtnDatas.Add(data);
            }
        }
        UIContainer_SetLanguageBtn.Refresh<UIWidget_SetLanguageBtn>(setLanguageBtnDatas.ToArray(), false);
    }

    private void OnCloseBtn()
    {
        Close();
    }

    private void OnEventLanguageChange(EventLanguageChange data)
    {
        Close();
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        EventBus.Unsubscribe<EventLanguageChange>(OnEventLanguageChange);
    }
}