using Framework;
using TMGame;
using UnityEngine.UI;

public class UIWidget_CoinItem : UIWidget_CoinItemBase
{
    public ShopItemViewParam drivedParam;

    protected override void OnCreate()
    {
        base.OnCreate();
        drivedParam = ViewData as ShopItemViewParam;
        Refresh(drivedParam);
    }

    protected override void RegisterGameEvent()
    {
        base.RegisterGameEvent();
        UIBtn_Buy.onClick.AddListener(OnPurchaseButtonClicked);
    }

    protected override void RemoveGameEvent()
    {
        base.RemoveGameEvent();
        UIBtn_Buy.onClick.RemoveListener(OnPurchaseButtonClicked);
    }

    private void Refresh(ShopItemViewParam param)
    {
        drivedParam = param;
        UITxt_Num.SetText($"{drivedParam.ItemData.ItemCnt[0]}");
        UITxt_NumRed.SetText($"{drivedParam.ItemData.ItemCnt[0]}");

        var thePrice = TMGame.GameGlobal.GetMod<IAPSys>().GetPrice(drivedParam.ItemData.Id);
        if(string.IsNullOrEmpty(thePrice))
        {
            UIBtn_Watch.gameObject.SetActive(true);
            UIBtn_Buy.gameObject.SetActive(false);
            UITxt_Num.gameObject.SetActive(false);
            UITxt_NumRed.gameObject.SetActive(true);
        }
        else
        {
            UIOldTxt_Price.text = thePrice;
            UIBtn_Watch.gameObject.SetActive(false);
            UIBtn_Buy.gameObject.SetActive(true);
            UITxt_Num.gameObject.SetActive(true);
            UITxt_NumRed.gameObject.SetActive(false);
        }
        
    }

    private void OnPurchaseButtonClicked()
    {
        drivedParam.buyOnClickItem.Invoke(drivedParam.ItemData, this);
    }
}
