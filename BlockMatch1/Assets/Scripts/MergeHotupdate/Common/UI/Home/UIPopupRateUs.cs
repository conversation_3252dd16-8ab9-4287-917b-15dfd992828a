using System;
using DragonPlus.Config.Global;
using DragonPlus.Core;
using DragonPlus.GooglePlay;
using DragonPlus.Save;
using Framework;
using TMGame;
using TMGame.Storage;
using UnityEngine.UI;

public class UIPopupRateUs : UIPopupRateUsBase
{
    protected override void OnCreate()
    {
        base.OnCreate();
        UIBtn_Close.onClick.AddListener(OnClickClose);
        UIBtn_Later.onClick.AddListener(OnClickLater);
        UIBtn_First.onClick.AddListener(OnClickFirst);
    }
    
    protected override void OnShow()
    {
        base.OnShow();
        SDK<IGooglePlayReview>.Instance.Prepare();

    }
    private void OnClickClose()
    {
        var storage = SDK<IStorage>.Instance.Get<StorageGlobal>();
        storage.RateUsFinish = true;
        // GameBIManager.Instance.SendGameEvent(BiEventCooking.Types.GameEventType.GameEventRateus, "quit", LevelGroupSystem.Instance.GetPassedLevelCount().ToString());

        // AudioManager.Instance.PlaySound(SfxNameConst.button_s);
        Close();
    }

    private void OnClickLater()
    {
       // AudioManager.Instance.PlaySound(SfxNameConst.button_s);
       var storage = SDK<IStorage>.Instance.Get<StorageGlobal>();
       storage.RateUsFinish = true;

       // GameBIManager.Instance.SendGameEvent(BiEventCooking.Types.GameEventType.GameEventRateus, "contact", LevelGroupSystem.Instance.GetPassedLevelCount().ToString());
       // UIManager.Instance.OpenHomeWindow(UINameConst.UIContactUs);
       Close();
    }

    private void OnClickFirst()
    {
        // AudioManager.Instance.PlaySound(SfxNameConst.button_s);
        var storage = SDK<IStorage>.Instance.Get<StorageGlobal>();
        storage.RateUsFinish = true;
        TMUtility.TyrOpenAppStore();

        // GameBIManager.Instance.SendGameEvent(BiEventCooking.Types.GameEventType.GameEventRateus, "contact", LevelGroupSystem.Instance.GetPassedLevelCount().ToString());
        // UIManager.Instance.OpenHomeWindow(UINameConst.UIContactUs);
        Close();
    }

    public void InitPopups()
    {
        var storage = SDK<IStorage>.Instance.Get<StorageGlobal>();
        if (storage.RateUsFinish)
            return ;
        GameGlobal.GetMod<ModPopup>().AddPopup(UIViewName.UIView_RateUsSubmit, CanShowUI, null);
    }

    private bool CanShowUI()
    {
        var storage = SDK<IStorage>.Instance.Get<StorageGlobal>();
        if (storage.RateUsFinish)
            return false;
        var commonSorage = SDK<IStorage>.Instance.Get<StorageCommon>();
        var nowTime = (long)TMUtility.GetTimeStamp();
        int day =  TMUtility.GetTotalDays((ulong)nowTime) - TMUtility.GetTotalDays((ulong)commonSorage.InstalledAt);
        if (day < GameGlobal.GetMgr<ConfigMgr>().GetConstConfig<Table_Global_Global,int>("RateUsPopUnlock"))
            return false;
        return true;
    }
}
