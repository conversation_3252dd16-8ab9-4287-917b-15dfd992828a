using System.Collections.Generic;
using UnityEngine;

namespace Spine.Unity
{
    public class SpineAnimationManager : MonoBehaviour
    {
        private Dictionary<string, SpineAnimationEvent> _spineNodes = new Dictionary<string, SpineAnimationEvent>();

        void Awake()
        {
            SpineAnimationEvent[] spineAnimationEvents = transform.GetComponentsInChildren<SpineAnimationEvent>();
            foreach (var animationevent in spineAnimationEvents)
            {
                if (!_spineNodes.ContainsKey(animationevent.gameObject.name))
                {
                    _spineNodes.Add(animationevent.gameObject.name, animationevent);
                }
            }

            SpineAnimationEvent spe = transform.GetComponent<SpineAnimationEvent>();
            if (spe != null)
            {
                _spineNodes.Add("_self", spe);
            }
        }

        public void PlaySpineAnimation(string animation)
        {
            if (string.IsNullOrEmpty(animation))
            {
                Debug.LogWarning("SpineAnimationEventParam is null !");
                return;
            }

            string[] param = animation.Split('#');
            if (param.Length < 2)
            {
                Debug.LogWarning($"SpineAnimationEventParam format error {animation} !");
                return;
            }

            string nodename = param[0];
            if (string.IsNullOrEmpty(nodename))
            {
                nodename = "_self";
            }

            bool clear_track = false;
            if (param.Length > 2)
            {
                clear_track = (param[2] == "1");
            }

            if (_spineNodes.ContainsKey(nodename))
            {
                _spineNodes[nodename].PlaySpineAnimation(param[1], clear_track);
            }
        }

        public void PlaySpineAnimationLoop(string animation)
        {
            if (string.IsNullOrEmpty(animation))
            {
                Debug.LogWarning("SpineAnimationEventParam is null !");
                return;
            }

            string[] param = animation.Split('#');
            if (param.Length < 2)
            {
                Debug.LogWarning($"SpineAnimationEventParam format error {animation} !");
                return;
            }

            bool clear_track = false;
            if (param.Length > 2)
            {
                clear_track = (param[2] == "1");
            }

            string nodename = param[0];
            if (string.IsNullOrEmpty(nodename))
            {
                nodename = "_self";
            }

            if (_spineNodes.ContainsKey(nodename))
            {
                _spineNodes[nodename].PlaySpineAnimationLoop(param[1], clear_track);
            }
        }

        /*
         * paramString格式（大小写敏感） - key1:value1#key2:value2
         * 格式解析：Node:01#CTrack:1#AniName01:first_confirm#Loop01:0#AniName02:Normal#Loop02:1
         * Node:节点名称                                                示例 Node:01
         * CTrack:立即播放设置动画，不考虑融合当前正在播放的动画              示例 CTrack:1
         * AniName01:第一个动画的名称                                     示例 AniName01:first_comfirm
         * Loop01:第一个动画是否循环                                      示例 Loop01:0
         * AniName02:第二个动画的名称                                     示例 AniName02:Normal
         * Loop02:第二个动画是否循环                                      示例 Loop02:1
         */
        public void PlaySpineAnimationKeyValueType(string paramString)
        {
            if (string.IsNullOrEmpty(paramString))
            {
                Debug.LogWarning("PlaySpineAnimationMulityParam paramString is null !");
                return;
            }

            string nodeName = null;
            bool clearTrack = false;
            string animationName01 = null;
            bool loop01 = false;
            string animationName02 = null;
            bool loop02 = false;
            
            string[] param = paramString.Split('#');
            for (int i = 0; i < param.Length; i++)
            {
                if(string.IsNullOrEmpty(param[i])) continue;
                string[] subParam = param[i].Split(':');
                if(subParam.Length != 2) continue;
                if(string.IsNullOrEmpty(subParam[0]) || string.IsNullOrEmpty(subParam[1])) continue;
                if (subParam[0].Equals("Node")) nodeName = subParam[1];
                else if(subParam[0].Equals("CTrack")) clearTrack = subParam[1].Equals("1");
                else if(subParam[0].Equals("AniName01")) animationName01 = subParam[1];
                else if(subParam[0].Equals("Loop01")) loop01 = subParam[1].Equals("1");
                else if(subParam[0].Equals("AniName02")) animationName02 = subParam[1];
                else if(subParam[0].Equals("Loop02")) loop02 = subParam[1].Equals("1");
            }
            if (!string.IsNullOrEmpty(nodeName))
            {
                SpineAnimationEvent animationEvent;
                if (_spineNodes.TryGetValue(nodeName, out animationEvent))
                {
                    animationEvent.PlaySpineAnimation(animationName01, clearTrack, loop01, animationName02, loop02);
                }
            }
        }
    }
}

