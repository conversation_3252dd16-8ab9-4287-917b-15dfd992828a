using System;
using System.Collections.Generic;
using DecorationRom.Event;
using DragonPlus;
using DragonPlus.Core;
using Framework;
using TMGame;
using UnityEngine.UI;

public class UIView_PopupNoMoney : UIView_PopupNoMoneyBase
{
    public class  ViewParams
    {
        public Action<int> OnClickEvent;
        public ViewParams(Action<int> action)
        {
            OnClickEvent = action;
        }
    }
    public static void ShowUI(Action<int> callBack = null)
    {
        ViewParams parms = new ViewParams(callBack);
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_PopupNoMoney,parms);
    }

    private ViewParams _params;

    protected override void OnCreate()
    {
        base.OnCreate();
        UIBtn_Close.onClick.AddListener(OnButtonCloseClick);
        UIBtn_ButtonStart.onClick.AddListener(OnButtonOkButtonClick);
        UIBtn_ButtonCancel.onClick.AddListener(OnButtonCloseClick);
        // if (ModABTestEx.IPlayGroupB())
        // {
        //     UIBtn_ButtonStart.transform.Find("Text").GetComponent<LocalizeTextMeshProUGUI>().SetText(CoreUtils.GetLocalization("UI_button_ok"));
        // }
    }

    protected override void OnOpen()
    {
        base.OnOpen();
        _params = ViewData as ViewParams;
    }

    private void OnButtonOkButtonClick()
    {
        if (ModABTestEx.IPlayGroupB())
        {
            Close();
            _params?.OnClickEvent?.Invoke(1);
            return;
        }
        _params?.OnClickEvent?.Invoke(1);
        // UIView_GameData uIView_GameData = new UIView_GameData();
        // GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Game, uIView_GameData);

        
        EventBus.Dispatch(new EventDailyTaskView());
       // GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_GameAdventure);
        var homeMain = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_HomeMain) as UIView_HomeMain;
        homeMain?.DisPoseTimer();
        Close();
        // var modFindTM = GameGlobal.GetMod<ModFindTM>();
        // var levelChallageUnlcok = GameGlobal.GetMgr<ConfigMgr>()
        //     .GetConstConfig<Table_Common_Global, int>("levelChallageUnlcok");
        // if (modFindTM.GeCurMainLevelIndex() >= levelChallageUnlcok)
        // {
        //     var openData = new UIView_FindTM_ChooseLevel.OpenData
        //     {
        //         modeType = EFindTMLevelModeType.Main,
        //         levelIndex = modFindTM.GeCurMainLevelIndex(),
        //     };
        //     Close();
        //     GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_FindTM_ChooseLevel, openData);
        // }
        // else
        // {
        //     if (  modFindTM.EnterGame(EFindTMLevelModeType.Main, modFindTM.GeCurMainLevelIndex(),
        //             new Dictionary<EItemType, bool>()))
        //     {
        //         GameGlobal.GetMgr<UIMgr>().Close(UIViewName.UIView_AreaTaskMain, true);
        //         Close();
        //     }
        // }
        //
    }

    private void OnButtonCloseClick()
    {
        Close();
        _params?.OnClickEvent?.Invoke(0);

    }
}
