using UnityEngine;
using System.Collections;
using Framework;
using TMGame;

public class TimeScheduler{
    public delegate void Task();

    public static CoroutineHandler Schedule(float delay, Task task)
    {
       return GameGlobal.GetMod<ModCoroutine>().StartCoroutine(DoTask(task, delay));
    }

    public static void Stop(CoroutineHandler coroutineTask)
    {
        GameGlobal.GetMod<ModCoroutine>().StopCoroutine(coroutineTask);
    }

    private static IEnumerator DoTask(Task task, float delay)
    {
        yield return new WaitForSeconds(delay);
        task();
    }
    
}
