using Framework;
using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using TMGame.Storage;
using TMGame;
using UnityEngine;
using DG.Tweening;
using EpForceDirectedGraph.cs;
using DecorationRom.Event;
using DecorationRom;
using DragonPlus.Core;
using DragonU3DSDK.Network.API.Protocol;
using Gameplay.BI;

public class UISubView_DecorationMain : UISubView_DecorationMainBase
{
    private StorageRoom _storageRoom;
    private Dictionary<int, UIBubble> _uiBubbles = new Dictionary<int, UIBubble>(8);
    private UIItemGroup _selectUI;
    public Action<bool> OnShowOrHideBackBtn;

    private static readonly Vector3 ShowPosition = Vector3.zero;
    private static readonly Vector3 HidePosition = new Vector3(10000, 10000, 0);

    public int RoomNodeId;
    public int ShowType = -1;
    public Action<int> OnBackToHome;
    private bool IsEnterFinishRoom;

    public Transform GetTargetBtn()
    {
        return _selectUI._btnOK.transform;
    }

    public Transform GetClearTarget()
    {
       return _selectUI._uiItems[0]._gameObject.transform;
    }

    public Transform GetBackTarget()
    {
        return UIBtn_CloseButton.transform;
    }

    protected override void OnCreate()
    {
        base.OnCreate();
        // 获取父对象上的 DecorationMainMono 组件
        DecorationMainMono mainMono = GO.transform.parent.gameObject.GetComponentOrAdd<DecorationMainMono>();
        // 注册指针抬起事件
        mainMono.OnPoinitUp = OnPointerUp;
        // 初始化组件
        InitializeComponents();
        // 注册事件
        RegisterEvents();
        // 添加按钮点击事件监听器
        UIBtn_RoomList.onClick.AddListener(OnClickRoomList);
        UIBtn_Back.onClick.AddListener(OnClickBack);
        UIBtn_Box.onClick.AddListener(OnClickBox);
        UINode_SelectUI.anchoredPosition = new Vector3(0, -200, 0);
        UIBtn_CloseButton.onClick.AddListener(OnClickCloseBtn);
        UIBtn_Diamond.onClick.AddListener(OnClickDiamond);
        UINode_SafeArea.gameObject.SetActive(false);
        UINode_ScreenOrigin.gameObject.SetActive(false);
        UINode_DecorationCurrency.gameObject.SetActive(true);
    }

    private void OnClickDiamond()
    {
        UIView_PopupNoMoney.ShowUI((code) =>
        {
            if (code == 1)
            {
                OnBackToHome?.Invoke(code);
            }
        });
    }

    private void OnClickBack()
    {
        if (!canClick)
        {
            return;
        }
        GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_common_1");
        int lastUnlockedRoomId = RoomManager.Instance.GetLastUnLockRoomId();
        if (RoomResSystem.Instance.IsRoomResReady(lastUnlockedRoomId))
        {
            RoomManager.Instance.CurRoomId = lastUnlockedRoomId;
            RoomManager.Instance.EnterRoom(lastUnlockedRoomId, true,
                (isSuccess) => { OnOpenRoom(new TryOpenRoomEvent(lastUnlockedRoomId, 0)); });
        }
        else if (RoomResSystem.Instance.IsRoomResReady(RoomManager.Instance.CurRoomId))
        {
            RoomManager.Instance.EnterRoom(RoomManager.Instance.CurRoomId, true,
                (isSuccess) => { OnOpenRoom(new TryOpenRoomEvent(RoomManager.Instance.CurRoomId, 0)); });
        }
    }

    private void OnClickBox()
    {
        if (!canClick)
        {
            return;
        }

        if (isShowRewardsing)
        {
            return;
        }

        InitRewards();
        UINode_Form.gameObject.SetActive(true);
        isShowRewardsing = true;
    }


    // 处理关闭按钮点击事件
    private void OnClickCloseBtn()
    {
        if (!canClick)
        {
            return;
        }
        GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_common_1");
        var guide = GameGlobal.GetMod<GuideSys>();
        if (guide.GetCurGuideId() == "GUIDE_103")
        {
            guide.FinishCurrent(GuideTargetType.BackHome);
        }

        //特殊处理，b组引导
        if (GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.ABTestGuide) == EABTestGroup.Group2)
        {
            BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventFteTestClickReturnButton);
        }
        OnBackToHome?.Invoke(0);
        HideBubbles();

    }

    public void KillTween()
    {
        if (!this.Visible)
        {
            return;
        }

        DOTween.Kill(UITxt_Count3);
    }

    public void ReSetNumber(string value)
    {
        UITxt_Count3.SetTerm(value);
    }

    // 处理房间动画结束事件
    private void OnRoomAnimationEnd(RoomAnimationEndEvent endEvent)
    {
        if (endEvent.IsCleanRoom || endEvent.IsFinished)
        {
            ShowBubbles();
            RefRoomProgress(RoomManager.Instance.CurRoomId);
        }
        else if (!endEvent.replaceNode)
        {
            _selectUI?.OnUnlocknextRoom();
            RefRoomProgress(RoomManager.Instance.CurRoomId);
        }
        
        var guide = GameGlobal.GetMod<GuideSys>();
        if (!guide.IsFinished("GUIDE_103"))
        {
            guide.Trigger(GuideTrigger.BackHome,"GUIDE_103");
        }
    }

    protected List<FormItem> allItem = new List<FormItem>();

    private void InitRewards()
    {
        var list = RoomManager.Instance.GetRoomAwards();
        if (null == list)
        {
            return;
        }

        for (int i = 0; i < list.Count; i++)
        {
            FormItem item = GetItem(i);
            item.SetData(list[i]);
        }
    }

    private bool isShowRewardsing = false;

    protected override void OnUpdate()
    {
        base.OnUpdate();
        if (!canClick)
        {
            return;
        }

        if (Input.GetMouseButtonDown(0))
        {
            if (!isShowRewardsing)
            {
                return;
            }

            isShowRewardsing = false;
            UINode_Form.gameObject.SetActive(false);
        }
    }

    private FormItem GetItem(int i)
    {
        var parent = i < 3 ? UINode_par1 : UINode_par2;
        if (allItem.Count > i)
        {
            allItem[i].SetParent(parent.transform);
            return allItem[i];
        }

        FormItem item = OpenUIWidget<FormItem>(parent.transform, false);
        allItem.Add(item);

        return item;
    }

    private bool canClick = true;


    private void RefRoomProgressB(int roomId)
    {
        int decNum = 0;
        int rvNum = 0;
        var room = RoomManager.Instance.CurrentInRoom.Data.GetStorageRoom();
        var roomStorage = RoomManager.Instance.GetStorageRoom();
        if (roomStorage.IsGetAward)
        {
            room.LastProgress = 1f;
            return;
        }
        float progres = RoomManager.Instance.GetRoomDecorationRate(roomId, ref decNum, ref rvNum);
        bool canAward = progres >= 1f && !roomStorage.IsGetAward;
        if (canAward)
        {
            roomStorage.IsGetAward = true;
           // OpenUnlockRoomUI();
        }

        CheckUnLockNextRoom();
    }

    private async UniTaskVoid RefRoomProgress(int roomId)
    {
        GameUtils.SetEventSystemEnable(true);
        CLog.Info($"========RefRoomProgress:{roomId}===========");
        if (ModABTestEx.IPlayGroupB())
        {
            RefRoomProgressB(roomId);
            return;
        }
        int decNum = 0;
        int rvNum = 0;
        var room = RoomManager.Instance.CurrentInRoom.Data.GetStorageRoom();
        var roomStorage = RoomManager.Instance.GetStorageRoom();
        int totalcount = RoomManager.Instance.GetRoomNodeCount(roomId);

        bool awarded = roomStorage.IsGetAward;
        UINode_BoxIconClose.gameObject.SetActive(!awarded);
        UINode_BoxIconOpen.gameObject.SetActive(awarded);
        if (roomStorage.IsGetAward)
        {
            room.LastProgress = 1f;
            UISlider_DownLoadSlider.value = 1;
            UITxt_DownLoadLabel.SetText($"{CalculatePercentage(totalcount,totalcount)}%");
            return;
        }

        float progres = RoomManager.Instance.GetRoomDecorationRate(roomId, ref decNum, ref rvNum);
        bool canAward = progres >= 1f && !roomStorage.IsGetAward;
        CLog.Info($"========RefRoomProgress:{roomId} canAward:{canAward}===========");

        var rewards = RoomManager.Instance.GetRoomAwards();
        if (canAward)
        {
            GameUtils.SetEventSystemEnable(false);
            // BIHelper.SendGameEvent(BiEventFindTm1.Types.GameEventType.GameEventPassRoom, RoomManager.Instance.CurRoomId.ToString(),
            //     Game.GetMod<ModFindTM>().GeCurMainLevelIndex().ToString());
            // BIHelper.SendDecorationInfo(BIHelper.EDecorationInfoType.CompleteRoom, RoomManager.Instance.CurRoomId, RoomManager.Instance.GetCurRoomNodeId());
            canClick = false;
            foreach (var reward in rewards)
            {
                GameGlobal.GetMod<UserProfileSys>().SettleReward(reward,
                    new BIHelper.ItemChangeReasonArgs
                    (
                        BiEventBlockMatch1.Types.ItemChangeReason.Deco
                    )
                    {
                        data1 = reward.ItemId.ToString(),
                        data2 = reward.Amount.ToString()
                    });
                {
                    
                }
                //  Game.Mod<ModFly>().AddItem(reward.GetItemType(), reward.Amount);
            }
            
            RoomManager.Instance.GetStorageRoom().IsGetAward = true;
        }

        UITxt_DownLoadLabel.SetText($"{CalculatePercentage(decNum,totalcount)}%");
        UISlider_DownLoadSlider.value = room.LastProgress;
        if (!Mathf.Approximately(room.LastProgress, progres))
        {
            await UniTask.Delay(TimeSpan.FromSeconds(1f));
            // GameUtils.SetEventSystemEnable(true);
            UISlider_DownLoadSlider.DOKill();
            UISlider_DownLoadSlider.DOValue(progres, 0.5f);
            room.LastProgress = progres;
        }
        await UniTask.Delay(TimeSpan.FromSeconds(0.5f));
        if (canAward)
        {
            BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventPassRoom,roomId.ToString());
            //show奖励UI
            // GameUtils.SetEventSystemEnable(false);
            await UniTask.Delay(TimeSpan.FromSeconds(0.5f));
            UIGetRewardParam rewardParam = new UIGetRewardParam();

            rewardParam.itemDatas = rewards;
            rewardParam.fly = false;
             rewardParam.closeAction = CheckUnLockNextRoom;
            rewardParam.onClickEvent = RefBox;
            GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.CommonGetReward, rewardParam);
            CLog.Info($"========RefRoomProgress:{roomId} canAward end ===========");
        }
        else
        {
            CheckUnLockNextRoom();
        }
        
        GameUtils.SetEventSystemEnable(true);
        canClick = true;
    }

    private void CheckUnLockNextRoom()
    {
        int lastUnlockedRoomId = RoomManager.Instance.GetLastUnLockRoomId();
        if (RoomManager.Instance.IsRoomFinish(lastUnlockedRoomId))
        {
            OpenUnlockRoomUI();
        }
    }

    private void OnRoomProgressAnimationEnd(RoomEndProgressRefresh endEvent)
    {
        if (endEvent.IsFinished)
        {
            OnlyRefreshProgress(endEvent.Id);
        }
    }

    private async UniTask OnlyRefreshProgress(int roomId)
    {
        int decNum = 0;
        int rvNum = 0;
        var room = RoomManager.Instance.CurrentInRoom.Data.GetStorageRoom();
        int totalcount = RoomManager.Instance.GetRoomNodeCount(roomId);
        float progres = RoomManager.Instance.GetRoomDecorationRate(roomId, ref decNum, ref rvNum);
        UITxt_DownLoadLabel.SetText($"{CalculatePercentage(decNum,totalcount)}%");
        UISlider_DownLoadSlider.value = room.LastProgress;
        await UniTask.Delay(TimeSpan.FromSeconds(1f));
        // GameUtils.SetEventSystemEnable(true);
        UISlider_DownLoadSlider.DOKill();
        UISlider_DownLoadSlider.DOValue(progres, 0.5f);
        room.LastProgress = progres;
    }

    private int CalculatePercentage(int num1, int num2)
    {
        if (num2 == 0)
        {
            throw new DivideByZeroException("The second number cannot be zero.");
            return 0;
        }

        // 计算百分比并四舍五入
        double result = (num1 * 1.0f / num2) * 100;
        return (int)Math.Round(result);
    }

    private void RefBox()
    {
        var roomStorage = RoomManager.Instance.GetStorageRoom();
        bool awarded = roomStorage.IsGetAward;
        UINode_BoxIconClose.gameObject.SetActive(!awarded);
        UINode_BoxIconOpen.gameObject.SetActive(awarded);
    }

    private void OpenUnlockRoomUI()
    {
        int lastUnlockedRoomId = RoomManager.Instance.GetLastUnLockRoomId();
        int nextRoomId = RoomManager.Instance.GetNextRoomId(lastUnlockedRoomId);
        if (nextRoomId< 0)
        {
            return;
        }
        UIView_UnlockRoom.OpenData openData = new UIView_UnlockRoom.OpenData()
        {
            nextRoomId = nextRoomId,
            showUnlockView = true
        };
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_UnlockRoom, openData);
    }
  
    private async UniTaskVoid RefCurRoomProgress()
    {
        int roomId = RoomManager.Instance.CurRoomId;
        int decNum = 0;
        int rvNum = 0;
        int totalcount = RoomManager.Instance.GetRoomNodeCount(roomId);
        float progres = RoomManager.Instance.GetRoomDecorationRate(roomId, ref decNum, ref rvNum);
        var room = RoomManager.Instance.CurrentInRoom.Data.GetStorageRoom();
        // UITxt_DownLoadLabel.SetText($"{decNum}/{totalcount}");
        UITxt_DownLoadLabel.SetText($"{CalculatePercentage(decNum,totalcount)}%");
        UISlider_DownLoadSlider.value = room.LastProgress;
        await UniTask.Delay(TimeSpan.FromSeconds(1f));
        // GameUtils.SetEventSystemEnable(true);
        UISlider_DownLoadSlider.DOKill();
        UISlider_DownLoadSlider.DOValue(progres, 0.5f);
        room.LastProgress = progres;
    }

    // 处理房间列表按钮点击事件
    private void OnClickRoomList()
    {
        GameGlobal.GetMgr<SoundMgr>().PlayButtonClick();
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_ChooseRoom);
    }

    // 打开视图时的处理逻辑
    protected override void OnOpen()
    {
        base.OnOpen();
        RoomBI.SendEnterRoomViewBI();
        ShowBubbles();
        RefRoomProgress(RoomManager.Instance.CurRoomId).Forget();
        if (ShowType == 0 || ShowType == 2)
        {
            ShowSelectedNode(RoomNodeId);
            OpenSelectUI();
            UIBtn_Back.gameObject.SetActive(false);
            UINode_SelectUI.gameObject.SetActive(true);
        }
        else if (ShowType == 1)
        {
            UINode_SelectUI.gameObject.SetActive(false);
        }

        isShowRewardsing = false;
        UINode_Form.gameObject.SetActive(false);
     //   if (!GameGlobal.GetMod<GuideSys>().IsFinished("GUIDE_101"))
        {

            GameGlobal.GetMod<GuideSys>().RegisterTarget(GuideTargetType.ConfirmClear, GetTargetBtn());
            GameGlobal.GetMod<GuideSys>().RegisterTarget(GuideTargetType.BackHome, GetBackTarget());
        }

        if (ModABTestEx.IPlayGroupB())
        {
            UIBtn_Box.gameObject.SetActive(false);
            UISlider_DownLoadSlider.gameObject.SetActive(false);
        }
    }

    // 关闭视图时的处理逻辑
    protected override void OnClose()
    {
        base.OnClose();
        ShowType = -1;
        RoomNodeId = 0;
        HideBubbles();
        UINode_Form.gameObject.SetActive(false);
        isShowRewardsing = false;
    }

    // 销毁视图时的处理逻辑
    protected override void OnDestroy()
    {
        base.OnDestroy();
        UnregisterEvents();
    }

    // 初始化组件
    private void InitializeComponents()
    {
        UINode_FurniturePointButton.gameObject.SetActive(false);
        UIBtn_FullScreenClose.gameObject.SetActive(false);
        _selectUI = new UIItemGroup(UINode_SelectUI.gameObject, OnSelectUIEvent);
        _selectUI.FlyItemFromPos = UINode_DecorationCurrency.Find("UIBtn_Diamond/Icon").position;
    }

    // 注册事件
    private void RegisterEvents()
    {
        EventBus.Subscribe<RoomAnimationEndEvent>(OnRoomAnimationEnd);
        EventBus.Subscribe<TryOpenRoomEvent>(OnOpenRoom);
        EventBus.Subscribe<RoomEndProgressRefresh>(OnRoomProgressAnimationEnd);
    }


    private void OnOpenRoom(TryOpenRoomEvent obj)
    {
        if (obj.ShowType == 1)
        {
            IsEnterFinishRoom = true;
            UIBtn_Back.gameObject.SetActive(true);
            // 检查是否有未解锁的RV广告节点，如果有则显示气泡，否则隐藏
            if (HasUnlockedRvNodes())
            {
                ShowBubbles();
            }
            else
            {
                HideBubbles();
            }
        }
        else if (obj.ShowType == 0)
        {
            IsEnterFinishRoom = false;
            ShowBubbles();
            // RefRoomProgress();
            UIBtn_Back.gameObject.SetActive(false);
        }
        else if (obj.ShowType == 2)
        {
            IsEnterFinishRoom = false;
            ShowBubbles();
            UIBtn_Back.gameObject.SetActive(false);
            CloseSelectUI();
        }

        RefRoomProgress(obj.RoomID);
    }

    // 取消注册事件
    private void UnregisterEvents()
    {
        EventBus.Unsubscribe<RoomAnimationEndEvent>(OnRoomAnimationEnd);
        EventBus.Unsubscribe<TryOpenRoomEvent>(OnOpenRoom);
        EventBus.Unsubscribe<RoomEndProgressRefresh>(OnRoomProgressAnimationEnd);
    }

    // 清理房间结束后的处理逻辑
    private void OnCleanRoomEnd()
    {
    }

    // 处理指针抬起事件
    private void OnPointerUp(Vector3 position)
    {
        if (RoomTimeLineManager.Instance.isPlayAnim) return;
        var selectedNode = RoomManager.Instance.CurrentInRoom.TapNode(position);
        if (selectedNode == null)
        {
            _selectUI?.CloseSelectUI();
            return;
        }

        GameGlobal.GetMgr<SoundMgr>().PlaySfx(202);
        HideBubbles();
        UIBtn_Back.gameObject.SetActive(false);
        UIBtn_FullScreenClose.gameObject.SetActive(false);
        _selectUI.SetData(selectedNode.Config);
        OpenSelectUI();
        UINode_SelectUI.gameObject.SetActive(true);
    }

    // 打开选择UI
    public void OpenSelectUI()
    {
        TableRoomNode nodeConfig = TableConfigManage.Instance.GetRoomNode(RoomNodeId);
        UINode_SelectUI.DOAnchorPosY(227f, 0.5f);
    }


    // 关闭选择UI
    private void CloseSelectUI()
    {
        // GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_common_1");
        UINode_SelectUI.DOAnchorPosY(-500f, 0.5f);
    }

    // 显示选择节点
    public void ShowSelectedNode(int roomNodeId)
    {
        TableRoomNode nodeConfig = TableConfigManage.Instance.GetRoomNode(roomNodeId);
        var nodeStatus = RoomManager.Instance.CurrentInRoom.Data.GetNodeStatus(nodeConfig.id);

        switch (nodeStatus)
        {
            case RoomItem.Status.Lock:
                break;

            case RoomItem.Status.Received:
            case RoomItem.Status.UnLock:
                HandleUnlockStatus(nodeConfig);
                break;
        }
    }

    // 处理解锁状态
    private void HandleUnlockStatus(TableRoomNode nodeConfig)
    {
        UIBtn_FullScreenClose.gameObject.SetActive(false);
        GameGlobal.GetMgr<SoundMgr>().PlaySfx(200);
        _selectUI.SetData(nodeConfig);
    }

    // 处理选择UI事件
    private void OnSelectUIEvent(UIItemGroup.Event e)
    {
     
        
        if (e == UIItemGroup.Event.SelectionCancel || e == UIItemGroup.Event.SelectionConfirm)
        {
            RoomManager.Instance.CurrentInRoom?.FocusOff();
            if (UIBtn_FullScreenClose != null)
            {
                UIBtn_FullScreenClose.gameObject.SetActive(false);
            }

            ShowBubbles();
            CloseSelectUI();
            GameUtils.SetEventSystemEnable(true);
        }
        else if (e == UIItemGroup.Event.CloseSelectUI)
        {
            CloseSelectUI();
            HideBubbles();
        }
        else if (e == UIItemGroup.Event.OnClickBackToHome)
        {
            OnBackToHome?.Invoke(1);
        }
    }

    #region Bubble

    // 显示气泡
    private void ShowBubbles()
    {
        if (IsEnterFinishRoom)
        {
            UIBtn_Back.gameObject.SetActive(true);
            // 检查是否有未解锁的RV广告节点，如果有则仍然显示气泡
            if (HasUnlockedRvNodes())
            {
                UINode_ScreenOrigin.gameObject.SetActive(true);
                UpdateBubbles();
            }
            else
            {
                UINode_ScreenOrigin.gameObject.SetActive(false);
            }
        }
        else
        {
            UINode_ScreenOrigin.gameObject.SetActive(true);
            UIBtn_Back.gameObject.SetActive(false);
            int index = 0;
            foreach (var kv in _uiBubbles)
            {
                kv.Value._gameObject.transform.DOKill();
                kv.Value._gameObject.transform.localScale = Vector3.zero;
                kv.Value._gameObject.transform.DOScale(Vector3.one, 0.5f).SetDelay(index * 0.15f).SetEase(Ease.OutBack);
                index++;
            }

            UpdateBubbles();
            TryTriggerGuideTarget();
        }
    }

    // 隐藏气泡
    private void HideBubbles()
    {
        UINode_ScreenOrigin.gameObject.SetActive(false);
    }

    /// <summary>
    /// 检查当前房间是否有未解锁的RV广告节点
    /// </summary>
    /// <returns>如果有未解锁的RV广告节点返回true，否则返回false</returns>
    private bool HasUnlockedRvNodes()
    {
        var room = RoomManager.Instance.CurrentInRoom;
        if (room == null)
            return false;

        var roomNodes = TableConfigManage.Instance.GetTableRoomNode(room.Id);
        if (roomNodes == null)
            return false;

        foreach (var nodeConfig in roomNodes)
        {
            // 检查是否是RV广告节点且状态为UnLock（未解锁但可解锁）
            if (nodeConfig.isRvGet)
            {
                var nodeStatus = room.Data.GetNodeStatus(nodeConfig.id);
                if (nodeStatus == RoomItem.Status.UnLock)
                {
                    return true;
                }
            }
        }

        return false;
    }

    private BubbleFDG _bubbleFdg = new BubbleFDG();
    private List<IFDGNode> _nodesToSimulate = new List<IFDGNode>(32);

    // 更新气泡
    private void UpdateBubbles()
    {
        _storageRoom = RoomManager.Instance.GetStorageRoom();
        if (_storageRoom == null)
            return;

        List<int> removeBubbleIds = new List<int>();
        foreach (var kv in _uiBubbles)
        {
            if (!_storageRoom.RoomNodes.ContainsKey(kv.Key))
            {
                removeBubbleIds.Add(kv.Key);
                continue;
            }

            if (_storageRoom.RoomNodes[kv.Key].Status != (int)RoomItem.Status.UnLock)
                removeBubbleIds.Add(kv.Key);
        }

        foreach (var id in removeBubbleIds)
        {
            GameObjectFactory.Destroy(_uiBubbles[id].GameObject);
            _uiBubbles.Remove(id);
        }

        foreach (var kv in _storageRoom.RoomNodes)
        {
            if (kv.Value.Status != (int)RoomItem.Status.UnLock)
                continue;

            int id = (int)kv.Value.Id;
            if (_uiBubbles.ContainsKey(id))
                continue;

            TableRoomNode tableRoomNode = RoomManager.Instance.CurrentInRoom.Data.GetRoomNode(id);
            if (tableRoomNode == null)
                continue;

            UIBubble uiBubble = new UIBubble(tableRoomNode,
                GameObjectFactory.Clone(UINode_FurniturePointButton.gameObject),
                OnBubbleClick);

            _uiBubbles.Add(id, uiBubble);
            if (!GameGlobal.GetMod<GuideSys>().IsFinished("GUIDE_101"))
            {
                GameGlobal.GetMod<GuideSys>()
                    .RegisterTarget(GuideTargetType.FirstEnterGame, uiBubble._gameObject.transform, "0");
            }

            UpdateBubbleStatus(uiBubble, id);
        }

        _nodesToSimulate.Clear();
        foreach (var kv in _uiBubbles)
        {
            _nodesToSimulate.Add(kv.Value);
        }

        _bubbleFdg.StartSimulatePos(_nodesToSimulate, OnSetBubblePosition);
    }


    private void TryTriggerGuideTarget()
    {
        var modAbTest = GameGlobal.GetMod<ModABTest>();
        if (!GameGlobal.GetMod<GuideSys>().IsFinished("GUIDE_101"))
        {
            var guidGroup = modAbTest.GetABTestGroup(EABTestType.ABTestGuide);
            if (guidGroup == EABTestGroup.Group1)
            {
                GameGlobal.GetMod<GuideSys>().Trigger(GuideTrigger.FirstEnterGame, "0");
            }
            else if(guidGroup == EABTestGroup.Group2 && GameGlobal.GetMod<GuideSys>().IsFinished("GUIDE_106") )
            {
                GameGlobal.GetMod<GuideSys>().Trigger(GuideTrigger.FirstEnterGame, "0");
            }
        }
    }

    // 更新气泡状态
    private void UpdateBubbleStatus(UIBubble bubble, int nodeId)
    {
        var room = RoomManager.Instance.CurrentInRoom;
        var screenPosition = room.GetRoomNodeScreenPos(nodeId);

        Vector3 position = screenPosition / GameGlobal.GetMgr<UIMgr>().GetScreenCanvasScale();

        bubble.SetPosition(position, Space.Self);
        RoomUtility.FitUIPos(bubble.GameObject, UINode_SafeArea.gameObject);
        bubble.UpdateStatus();
        bubble.SetActive(true);
        position = bubble.GameObject.transform.localPosition;
        position.x += UnityEngine.Random.Range(-5f, 5f);
        position.y += UnityEngine.Random.Range(-5f, 5f);
        bubble.SetPosition(position, Space.Self);
    }

    // 设置气泡位置
    private void OnSetBubblePosition(int nodeId, AbstractVector iPosition)
    {
        if (_uiBubbles == null)
            return;

        UIBubble bubble;
        if (_uiBubbles.TryGetValue(nodeId, out bubble))
        {
            var trans = bubble.GameObject.transform;
            var newPos = new Vector3(iPosition.x, iPosition.y, trans.localPosition.z);
            trans.DOLocalMove(newPos, 1);
        }
    }

    // 处理气泡点击事件
    private void OnBubbleClick(UIBubble bubble, TableRoomNode nodeConfig)
    {
        var room = RoomManager.Instance.CurrentInRoom;
        var roomData = room.Data;
        var nodeStatus = roomData.GetNodeStatus(nodeConfig.id);

        var guide = GameGlobal.GetMod<GuideSys>();
        if (guide.GetCurGuideId() == "GUIDE_101")
        {
            guide.FinishCurrent(GuideTargetType.FirstEnterGame);
        }
        switch (nodeStatus)
        {
            case RoomItem.Status.Lock:
                break;
            case RoomItem.Status.Received:
            case RoomItem.Status.UnLock:
                HideBubbles();
                UIBtn_Back.gameObject.SetActive(false);
                ShowSelectedNode(nodeConfig.id);
                OpenSelectUI();
                UINode_SelectUI.gameObject.SetActive(true);
                break;
        }
    }

    #endregion
}