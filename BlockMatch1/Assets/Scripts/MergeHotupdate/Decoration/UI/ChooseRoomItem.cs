using System;
using Coffee.UIEffects;
using DecorationRom;
using DecorationRom.Event;
using DragonPlus;
using DragonPlus.Core;
using Framework;
using TMGame;
using UnityEngine;
using UnityEngine.UI;
using YooAsset;

public class ChooseRoomItem : ChooseRoomItemBase
{
    public RoomGroupType groupType;
    private int roomId;
    private bool update = false;
    private ResourceDownloaderOperation downLoaderInfo;

    private UIHsvModifier _modifier;
    private Image _titleBg;
    protected override void BindComponent()
    {
        base.BindComponent();
        _titleBg = UINode_UIBg.GetComponent<Image>();
    }

    protected override void OnCreate()
    {
        base.OnCreate();
        UIBtn_ViewButton.onClick.AddListener(OnClickView);
        UIBtn_DownLoad.onClick.AddListener(OnClickDownLoad);
        UIBtn_ContinueButton.onClick.AddListener(OnClicContine);
        _modifier = UIImg_Icon.GetComponent<UIHsvModifier>();
        UISlider_DownLoadSlider.gameObject.SetActive(false);
        EventBus.Subscribe<RoomDownLoadEndEvent>(OnRoomDownLoadEnd);
    }

    private void OnClicContine()
    {
        HandleRoomEnter(roomId, () =>
            {
                CloseChooseRoom();
                EventBus.Dispatch<TryOpenRoomEvent>(new TryOpenRoomEvent(roomId, 0));
            }

        );
    }

    private void OnClickView()
    {
        if (groupType != RoomGroupType.Finsh)
            return;

        int oldRoomId = RoomManager.Instance.CurRoomId;
        if (oldRoomId == roomId)
        {
            CloseChooseRoom();
            return;
        }

        HandleRoomEnter(roomId, () =>
        {
            EventBus.Dispatch<TryOpenRoomEvent>(new TryOpenRoomEvent(roomId, 1));
            CloseChooseRoom();
        });
    }

    private void OnClickDownLoad()
    {
        if (!IsInternetAvailable())
        {
            ShowDownloadFailPopup();
            return;
        }

        RoomResSystem.Instance.CheckAndDownloadAssets(roomId);
        RefDownLoaderInfo();
    }

    private void OnRoomDownLoadEnd(RoomDownLoadEndEvent obj)
    {
        if (obj.RoomId != this.roomId)
            return;

        if (obj.Status == EOperationStatus.Succeed)
        {
            SetData(roomId);
        }
        else
        {
            UpdateDownloadUI(false);
        }
    }

    private void RefDownLoaderInfo()
    {
        downLoaderInfo = RoomResSystem.Instance.GetDownloaderInfo(roomId);
        if (downLoaderInfo == null)
        {
            UpdateDownloadUI(false);
            UISlider_DownLoadSlider.value = 0;
            UITxt_DownLoadLabel.SetText("0%");
        }
        else
        {
            update = true;
            UpdateDownloadUI(true);
            UISlider_DownLoadSlider.value = downLoaderInfo.Progress;
            UITxt_DownLoadLabel.SetText($"{downLoaderInfo.Progress * 100:F2}%");
        }
    }

    public void SetData(int roomId)
    {
        this.roomId = roomId;

        groupType = DetermineGroupType( ref downLoaderInfo);

        TableRoom roomConfig = TableConfigManage.Instance.GetTableRoom(roomId);
        if (roomConfig == null)
        {
            CLog.Error($"not found room :{roomId}");
            return;
        }
        string imageName = GetImageName(groupType, roomConfig);
        UITxt_TitleText.SetTerm(roomConfig.roomName);
        UITxt_TitleText_UINode_UIBg.SetTerm(roomConfig.roomName);
        UITxt_TitleText_UINode_UIUnlock.SetTerm(roomConfig.roomName);
        if (!string.IsNullOrEmpty(imageName))
        {
            CoreUtils.SetImg(UIImg_Icon, CoreUtils.GetSprite("RoomAtlas", imageName, UIImg_Icon.gameObject));
        }

        UINode_BG.gameObject.SetActive(groupType == RoomGroupType.Normal);
        UINode_UIBg.gameObject.SetActive(groupType == RoomGroupType.Finsh || groupType == RoomGroupType.None);
        UINode_UIUnlock.gameObject.SetActive(groupType == RoomGroupType.Lock || groupType == RoomGroupType.ComingSoon);
        _modifier.enabled = groupType == RoomGroupType.Lock || groupType == RoomGroupType.ComingSoon;
        RefreshUI();
    }

    private void RefreshUI()
    {
        UINode_NormalGroup.gameObject.SetActive(groupType == RoomGroupType.Normal);
        UINode_LockGroup.gameObject.SetActive(groupType == RoomGroupType.Lock);
        UINode_FinishGroup.gameObject.SetActive(groupType == RoomGroupType.Finsh);
        UINode_DownLoadGroup.gameObject.SetActive(groupType == RoomGroupType.None);
        UINode_ComingSoonGroup.gameObject.SetActive(groupType == RoomGroupType.ComingSoon);
        UIImg_CurrentBG.gameObject.SetActive(groupType == RoomGroupType.Normal);
        UIImg_Other.gameObject.SetActive(groupType == RoomGroupType.Finsh || groupType == RoomGroupType.None);
        UIImg_Unlock.gameObject.SetActive(groupType == RoomGroupType.Lock || groupType == RoomGroupType.ComingSoon);
    }

  
    private void HandleRoomEnter(int roomId, Action onSuccess)
    {
        int oldRoomId = RoomManager.Instance.CurRoomId;
        if (oldRoomId == roomId)
        {
            onSuccess?.Invoke();
            return;
        }

        RoomManager.Instance.EnterRoom(roomId, true, (isSuccess) =>
        {
            if (!isSuccess)
            {
                CLog.Error("房间进入失败");
                return;
            }
            RoomManager.Instance.CurRoomId = roomId;
            onSuccess?.Invoke();
        });
    }

    private void CloseChooseRoom()
    {
        GameGlobal.GetMgr<UIMgr>().Close(UIViewName.UIView_ChooseRoom);
    }

    private bool IsInternetAvailable()
    {
        return Application.internetReachability != NetworkReachability.NotReachable;
    }

    private void ShowDownloadFailPopup()
    {
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIPopupDownLoadFail);
    }

    private void UpdateDownloadUI(bool isLoading)
    {
        UIBtn_DownLoad.gameObject.SetActive(!isLoading);
        UISlider_DownLoadSlider.gameObject.SetActive(isLoading);
    }

    private RoomGroupType DetermineGroupType(ref ResourceDownloaderOperation downLoaderInfo)
    {
        
        TableRoom roomConfig = TableConfigManage.Instance.GetTableRoom(roomId);
        if (roomConfig != null && roomConfig.isComingSoon)
            return RoomGroupType.ComingSoon;
        
        int count = RoomManager.Instance.GetRoomNodeCount(roomId);
        bool isUnLockRoom = RoomManager.Instance.IsUnLock(roomId);
        if (!isUnLockRoom)
        {
            return RoomGroupType.Lock;
        }
       
        if (!RoomResSystem.Instance.IsRoomResReady(this.roomId))
            return RoomGroupType.None;
        
        int decNum = 0;
        int rvNum = 0;
        float progress = RoomManager.Instance.GetRoomDecorationRate(roomId, ref decNum, ref rvNum);
        UISlider_Progress.value = progress;
        UITxt_ProgressLabel.SetText( $"{decNum}/{count}");
        return progress >= 1 ? RoomGroupType.Finsh : RoomGroupType.Normal;
    }

    private string GetImageName(RoomGroupType groupType, TableRoom roomConfig)
    {
        switch (groupType)
        {
            case RoomGroupType.Normal:
            case RoomGroupType.Finsh:
            case RoomGroupType.ComingSoon:
                return roomConfig?.mapImgComplete;
            case RoomGroupType.Lock:
                return roomConfig?.mapImgUndone;
            default:
                return roomConfig?.mapImgComplete;
        }
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        update = false;
        EventBus.Unsubscribe<RoomDownLoadEndEvent>(OnRoomDownLoadEnd);
    }

    protected override void OnUpdate()
    {
        base.OnUpdate();
        if (!update)
            return;

        UISlider_DownLoadSlider.value = downLoaderInfo.Progress;
        UITxt_DownLoadLabel.SetText( $"{downLoaderInfo.Progress * 100:F2}%");
    }
}
