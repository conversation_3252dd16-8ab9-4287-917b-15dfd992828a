using Spine.Unity;
using UnityEngine;

namespace DecorationRom.Core
{
    public class HomeSpinePlayAniamtion : MonoBehaviour
    {
        public string idleName;
        public string playName;

        public void OnEnable()
        {
            if (!string.IsNullOrEmpty(playName))
            {
                SkeletonAnimation[] skeletonAnimations = GetComponentsInChildren<SkeletonAnimation>();
                foreach (var p in skeletonAnimations)
                {
                    p.AnimationName = playName;
                    p.ClearState();
                    p.skeleton.SetSlotsToSetupPose();
                    p.AnimationName = null;
                    p.AnimationName = playName;
                    p.Update(0);
                }
            }
        }

        public void OnDisable()
        {
            if (!string.IsNullOrEmpty(idleName))
            {
                SkeletonAnimation[] skeletonAnimations = GetComponentsInChildren<SkeletonAnimation>();
                if (null== skeletonAnimations)
                {
                    return;
                }
                foreach (var p in skeletonAnimations)
                {
                    if (p == null)
                    {
                        continue;
                    }
                    p.AnimationName = idleName;
                    p.ClearState();
                    if (p.skeleton != null)
                    {
                        p.skeleton.SetSlotsToSetupPose();
                    }
                    p.AnimationName = null;
                    p.AnimationName = idleName;
                    p.Update(0);
                }
            }
        }
    }
}