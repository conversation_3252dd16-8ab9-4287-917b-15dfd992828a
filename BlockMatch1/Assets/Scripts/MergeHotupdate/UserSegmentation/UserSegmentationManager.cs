
using DragonPlus.Core;
using Framework;
using TMGame.Storage;
using UnityEngine;
using UnityEngine.UIElements;

namespace UserSegmentation
{
    public class UserSegmentationManager : Singleton<UserSegmentationManager>
    {
        private int _chooseStoryId = 0;
        private UserSegmentationEngineHelper _helper = null;

        public int ChooseStoryId => _chooseStoryId;
        public void Init()
        {
            
        }
        public void LoadUserSegmentation()
        {
            if (_helper == null)
            {
                _helper = new UserSegmentationEngineHelper();
            }
            DragonCD2.UserSegmentation.UserSegmentation.CreateBehaviroalEngine(_helper);
            _chooseStoryId = DragonCD2.UserSegmentation.UserSegmentation.GetBehavioralSegmentationId();
        }

        public int GetCurrentSkillId()
        {
            var skillId = DragonCD2.UserSegmentation.UserSegmentation.GetSkillId();
            if (skillId == -1)
            {
                CLog.Error("GetCurrentSkillId failed");
            }
            return skillId;
        }
    }
}