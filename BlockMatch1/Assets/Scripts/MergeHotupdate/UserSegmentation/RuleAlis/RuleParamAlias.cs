using System;
using System.Linq;
using DragonCD2.UserSegmentation.Rule.Parameter;
using DragonPlus.Core;
using DragonPlus.Save;
using Framework;
using TMGame;
using TMGame.Storage;
using UnityEngine;

namespace UserSegmentation
{
    // 当前分数
    public class CurrentScoreParam: FlexibleRuleParam
    {
        public override string Alias => "CurrentScore";

        public override object Value
        {
            get
            {
                var curScore = 0;
                var gameSys = GameGlobal.GetMod<ModGame>();
                if (gameSys != null && gameSys.CurGameType == EnumBlockGameType.BlockGame_Endless)
                {
                    curScore = gameSys.CurScore;
                }
                CLog.Info($"[无尽剧本] 用户当前的分数是[{curScore}]");
                return curScore;
            }
        }
    }


    // 近期(3局)平均分数
    public class RecentAvgScoresParam: FlexibleRuleParam
    {
        public override string Alias => "RecentAvgScores";

        public override object Value
        {
            get
            {
                var scores = SDK<IStorage>.Instance.Get<StorageGlobal>().GameEndlss.ClosestFivePlayScores;
                var newScores = scores.TakeLast(3);
                var enumerable = newScores as int[] ?? newScores.ToArray();
                var score = enumerable.Any()? enumerable.Average() : 0;
                CLog.Info($"[无尽剧本] 用户近期的平均分是[{score}]");
                return score;
            }
        }
    }

    // 历史最高分
    public class BestScoresParam: FlexibleRuleParam
    {
        public override string Alias => "BestScores";

        public override object Value
        {
            get
            {
                var bestScore = SDK<IStorage>.Instance.Get<StorageGlobal>().GameEndlss.CurFirstScore;
                CLog.Info($"[无尽剧本] 用户的历史最高分是[{bestScore}]");
                return bestScore;
            }
        }
    }

    // 生命周期
    public class LifecycleParam: FlexibleRuleParam
    {
        public override string Alias => "Lifecycle";

        public override object Value
        {
            get
            {
                var firstLoginTime = SDK<IStorage>.Instance.Get<StorageGlobal>().UserData.FirstLoginTime;
                if (firstLoginTime == 0)
                {
                    return 0;
                }
                var lifecycle = (DateTime.Now - new DateTime(1970, 1, 1).AddSeconds(firstLoginTime)).Days;
                CLog.Info($"[无尽剧本] 用户的生命周期是[{lifecycle}]");
                return lifecycle;
            }
        }
    }

    // 历史最高combo数
    public class BestCombosParam: FlexibleRuleParam
    {
        public override string Alias => "BestCombos";

        public override object Value
        {
            get
            {
                var bestCombos = SDK<IStorage>.Instance.Get<StorageGlobal>().GameEndlss.BestCombo;
                CLog.Info($"[无尽剧本] 用户的历史最高combo数是[{bestCombos}]");
                return bestCombos;
            }
        }
    }

    // 近期最高combo数
    public class RecentBestCombosParam: FlexibleRuleParam
    {
        public override string Alias => "RecentBestCombos";

        public override object Value
        {
            get
            {
                var scores = SDK<IStorage>.Instance.Get<StorageGlobal>().GameEndlss.ClosestThreePlayCombo;
                var enumerable = scores.ToArray();
                var recentBestCombos = enumerable.Any()? enumerable.Max() : 0;
                CLog.Info($"[无尽剧本] 用户的近期最高combo数是[{recentBestCombos}]");
                return recentBestCombos;
            }
        }
    }

    // 近期平均combo数
    public class RecentAvgCombosParam: FlexibleRuleParam
    {
        public override string Alias => "RecentAvgCombos";

        public override object Value
        {
            get
            {
                var scores = SDK<IStorage>.Instance.Get<StorageGlobal>().GameEndlss.ClosestThreePlayCombo;
                var enumerable = scores.ToArray();
                var recentAvgCombos = enumerable.Any()? enumerable.Average() : 0;
                CLog.Info($"[无尽剧本] 用户的近期平均combo数是[{recentAvgCombos}]");
                return recentAvgCombos;
            }
        }
    }

    // 本局目前连续combo数量
    public class CurrentCombosParam: FlexibleRuleParam
    {
        public override string Alias => "CurrentCombos";

        public override object Value
        {
            get
            {
                var currentCombos = 0;
                var gameSys = GameGlobal.GetMod<ModGame>();
                if (gameSys is { CurGameType: EnumBlockGameType.BlockGame_Endless })
                {
                    currentCombos = gameSys.CurrentComboNum;
                }
                CLog.Info($"[无尽剧本] 用户的当前连击是[{currentCombos}]");
                return currentCombos;
            }
        }
    }

    // 历史单局最高清屏次数
    public class BestClearScreensParam: FlexibleRuleParam
    {
        public override string Alias => "BestClearScreens";

        public override object Value
        {
            get
            {
                var bestClearScreens = SDK<IStorage>.Instance.Get<StorageGlobal>().GameEndlss.BestClearScreen;
                CLog.Info($"[无尽剧本] 用户的历史单局最高清屏次数是[{bestClearScreens}]");
                return bestClearScreens;
            }
        }
    }

    // 近期最高清屏次数
    public class RecentBestScreensParam: FlexibleRuleParam
    {
        public override string Alias => "RecentBestScreens";

        public override object Value
        {
            get
            {
                var recentBestScreens = 0;
                var scores = SDK<IStorage>.Instance.Get<StorageGlobal>().GameEndlss.ClosestThreeClearScreen;
                var enumerable = scores.ToArray();
                recentBestScreens = enumerable.Any()? enumerable.Max() : 0;
                CLog.Info($"[无尽剧本] 用户的近期最高清屏次数是[{recentBestScreens}]");
                return recentBestScreens;
            }
        }
    }

    // 近期平均清屏次数
    public class RecentAvgScreensParam: FlexibleRuleParam
    {
        public override string Alias => "RecentAvgScreens";

        public override object Value
        {
            get
            {
                var recentAvgScreens = 0;
                var scores = SDK<IStorage>.Instance.Get<StorageGlobal>().GameEndlss.ClosestThreeClearScreen;
                var enumerable = scores.ToArray();
                recentAvgScreens = (int)(enumerable.Any()? enumerable.Average() : 0);
                CLog.Info($"[无尽剧本] 用户的近期平均清屏次数是[{recentAvgScreens}]");
                return recentAvgScreens;
            }
        }
    }

    // 本局目前清屏的次数
    public class CurrentClearScreensParam: FlexibleRuleParam
    {
        public override string Alias => "CurrentClearScreens";

        public override object Value
        {
            get
            {
                var currentClearScreens = 0;
                var gameSys = GameGlobal.GetMod<ModGame>();
                if (gameSys is { CurGameType: EnumBlockGameType.BlockGame_Endless })
                {
                    currentClearScreens = gameSys.ClearScreenTime;
                }
                CLog.Info($"[无尽剧本] 用户的本局目前清屏的次数是[{currentClearScreens}]");
                return currentClearScreens;
            }
        }
    }

    // 总无尽游戏次数
    public class TotalEndlessGamesParam: FlexibleRuleParam
    {
        public override string Alias => "TotalEndlessGames";

        public override object Value
        {
            get
            {
                var totalEndlessGames = SDK<IStorage>.Instance.Get<StorageGlobal>().GameEndlss.EnterCount;
                CLog.Info($"[无尽剧本] 用户的总无尽游戏次数是[{totalEndlessGames}]");
                return totalEndlessGames;
            }
        }
    }

    // 距离上一次玩无尽模式多久(天)
    public class LastEndlessParam: FlexibleRuleParam
    {
        public override string Alias => "LastEndless";

        public override object Value
        {
            get
            {
                var lastEndless = 0;
                CLog.Info($"[无尽剧本] 用户距离上一次玩无尽模式[{lastEndless}](天)");
                return lastEndless;
            }
        }
    }

    // 对比历史最高分当前进度 {CurrentScore}/{BestScores}
    public class CurrentProgressParam: FlexibleRuleParam
    {
        public override string Alias => "CurrentProgress";

        public override object Value
        {
            get
            {
                var currentProgress = 0f;
                var gameSys = GameGlobal.GetMod<ModGame>();
                if (gameSys != null && gameSys.CurGameType == EnumBlockGameType.BlockGame_Endless)
                {
                    var currentScore = 0f;
                    currentScore = gameSys.CurScore;
                    var bestScore = SDK<IStorage>.Instance.Get<StorageGlobal>().GameEndlss.CurFirstScore;
                    currentProgress = currentScore / bestScore;
                }

                CLog.Info($"[无尽剧本] 用户对比历史最高分当前进度 [{currentProgress}]");
                return currentProgress;
            }
        }
    }

    // 本日各剧本被触发的次数
    public class EndlessTimesParam: FlexibleRuleParam
    {
        public override string Alias => "EndlessTimes";

        public override object Value
        {
            get
            {
                var endlessTimes = 0;//SDK<IStorage>.Instance.Get<StorageGlobal>().GameEndlss.EnterCount;
                CLog.Error($"[无尽剧本] 变量弃用，需要修改配置 ！【用户本日各剧本被触发的次数】");
                return endlessTimes;
            }
        }
    }

    // 当前局面的复杂度
    public class CurrentComplexityParam: FlexibleRuleParam
    {
        public override string Alias => "CurrentComplexity";

        public override object Value
        {
            get
            {
                var currentComplexity = BlockPlayManager.Instance.Complexity;
                CLog.Info($"[无尽剧本] 用户的当前局面的复杂度是[{currentComplexity}]");
                return currentComplexity;
            }
        }
    }

    // 难度分组
    public class ComplextiyGroupParam: FlexibleRuleParam
    {
        public override string Alias => "ComplextiyGroup";

        public override object Value
        {
            get
            {
                var complextiyGroup = 0;
                var gameSys = GameGlobal.GetMod<ModGame>();
                if (gameSys != null && gameSys.CurGameType == EnumBlockGameType.BlockGame_Endless)
                {
                    complextiyGroup = gameSys.ComplexityGroup;
                }
                CLog.Info($"[无尽剧本] 用户的难度分组是[{complextiyGroup}]");
                return complextiyGroup;
            }
        }
    }

    // 今日无尽游戏次数
    public class TodayEndlessTimesParam: FlexibleRuleParam
    {
        public override string Alias => "TodayEndlessTimes";

        public override object Value
        {
            get
            {
                var todayEndlessTimes = SDK<IStorage>.Instance.Get<StorageGlobal>().GameEndlss.TodayPlayCount;
                CLog.Info($"[无尽剧本] 用户的今日无尽游戏次数是[{todayEndlessTimes}]");
                return todayEndlessTimes;
            }
        }
    }

    // 距离上次破纪录已经玩了多少局无尽模式
    public class LastBestScoresTimesParam: FlexibleRuleParam
    {
        public override string Alias => "LastBestScoresTimes";

        public override object Value
        {
            get
            {
                var lastBestScoresTimes = SDK<IStorage>.Instance.Get<StorageGlobal>().GameEndlss.AfterBestScoreTimes;
                CLog.Info($"[无尽剧本] 用户距离上次破纪录已经玩了 [{lastBestScoresTimes}] 局无尽模式");
                return lastBestScoresTimes;
            }
        }
    }

    // 是否可以进入爽关剧本 {LastBestScoresTimes}%5 == 0 
    public class ToStory5Param: FlexibleRuleParam
    {
        public override string Alias => "ToStory5";

        public override object Value
        {
            get
            {
                var lastBestScoresTimes = SDK<IStorage>.Instance.Get<StorageGlobal>().GameEndlss.AfterBestScoreTimes;
                var toStory5 = 1;
                if (lastBestScoresTimes > 0)
                    toStory5 = lastBestScoresTimes % 5 == 0 ? 0 : 1;

                CLog.Info($"[无尽剧本] 用户是否可以进入爽关剧本 [{toStory5}]");
                return toStory5;
            }
        }
    }

    // 近期表现对比历史最高分系数 {RecentAvgScores}/{BestScores}
    public class RecentCoefficientParam: FlexibleRuleParam
    {
        public override string Alias => "RecentCoefficient";

        public override object Value
        {
            get
            {
                var recentCoefficient = 0f;
                var bestScore = SDK<IStorage>.Instance.Get<StorageGlobal>().GameEndlss.CurFirstScore;
                var scores = SDK<IStorage>.Instance.Get<StorageGlobal>().GameEndlss.ClosestFivePlayScores;
                var newScores = scores.TakeLast(3);
                var enumerable = newScores as int[] ?? newScores.ToArray();
                var avgScore = enumerable.Any()? enumerable.Average() : 0;
                if (bestScore > 0)
                {
                    recentCoefficient = (float)avgScore / bestScore;
                }
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (ModGame.Recent_Coefficient > 0)
                {
                    recentCoefficient = ModGame.Recent_Coefficient;
                    CLog.Info($"[无尽剧本] <color=red> 近期表现GM工具生效中 </color>");
                }
#endif
                CLog.Info($"[无尽剧本] 用户的近期表现对比历史最高分系数是[{recentCoefficient}]");
                return recentCoefficient;
            }
        }
    }

    // 当前分数超过历史最高分多少 {CurrentScore}-{BestScores} 
    public class ExceedBestScoresParam: FlexibleRuleParam
    {
        public override string Alias => "ExceedBestScores";

        public override object Value
        {
            get
            {
                var bestScore = SDK<IStorage>.Instance.Get<StorageGlobal>().GameEndlss.CurFirstScore;
                var exceedBestScores = 0;
                var gameSys = GameGlobal.GetMod<ModGame>();
                if (gameSys != null && gameSys.CurGameType == EnumBlockGameType.BlockGame_Endless)
                {
                    if (gameSys.CurScore > bestScore)
                        exceedBestScores = gameSys.CurScore - bestScore;
                }
                CLog.Info($"[无尽剧本] 用户当前分数超过历史最高分 [{exceedBestScores}]分");
                return exceedBestScores;
            }
        }
    }
}
