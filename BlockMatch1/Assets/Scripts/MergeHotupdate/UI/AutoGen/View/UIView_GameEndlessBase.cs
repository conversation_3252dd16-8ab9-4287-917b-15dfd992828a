/******************************/
/*****自动生成的UIView界面代码，禁止手动修改*****/
/*****界面逻辑写在子类中*****/
/*****生成时间：2025-5-20 14:39:14*****/
/*****************************/

using Framework;
using DragonPlus;
using UnityEngine;
using UnityEngine.UI;

public class UIView_GameEndlessBase : UIViewBase
{
	protected LocalizeTextMeshProUGUI UITxt_CombolCd;
	protected RectTransform UINode_Score;
	protected RectTransform UINode_Target;
	protected Button UIBtn_Close;
	protected Button UIBtn_SetUp;
	protected LocalizeTextMeshProUGUI UITxt_AdsCount;
	protected RectTransform UINode_Ads;
	protected RectTransform UINode_BlockPool;
	protected RectTransform UINode_Rank;
	protected RectTransform UINode_Content;
	protected RectTransform UINode_Refer;
	protected RectTransform UINode_Block1;
	protected RectTransform UINode_Block2;
	protected RectTransform UINode_Block3;
	protected RectTransform UINode_Area1;
	protected RectTransform UINode_Area2;
	protected RectTransform UINode_Area3;
	protected RectTransform UINode_FullShield;
	protected RectTransform UINode_BlockArea;
	protected RectTransform UINode_ContentSmail;
	protected RectTransform UINode_Down;
	protected RectTransform UINode_BlockBornEffect;
	protected RectTransform UINode_TipPlay;
	protected LocalizeTextMeshProUGUI UITxt_PType;
	protected LocalizeTextMeshProUGUI UITxt_Complex;
	protected LocalizeTextMeshProUGUI UITxt_BlockPlan;
	protected Button UIBtn_Debug;
	protected RectTransform UINode_Main;
	protected RectTransform UINode_Tip;
	protected LocalizeTextMeshProUGUI UITxt_BlockOrder;
	protected LocalizeTextMeshProUGUI UITxt_Complexlnfo;
	protected Button UIBtn_Random;
	protected Button UIBtn_Hard;
	protected Button UIBtn_ComplexityUp;
	protected Button UIBtn_ComplexityDown;
	protected Button UIBtn_MultiClear;
	protected Button UIBtn_ClearScreen;
	protected RectTransform UINode_Debug;
	protected RectTransform UINode_PutTip;
	protected RectTransform UINode_finger1;
	protected RectTransform UINode_finger2;
	protected RectTransform UINode_TempFinger1;

    protected override void BindComponent()
    {
		UITxt_CombolCd = GO.transform.Find("Root/UINode_Main/UINode_Score/UITxt_CombolCd").GetComponent<LocalizeTextMeshProUGUI>();
		UINode_Score = GO.transform.Find("Root/UINode_Main/UINode_Score").GetComponent<RectTransform>();
		UINode_Target = GO.transform.Find("Root/UINode_Main/UINode_Target").GetComponent<RectTransform>();
		UIBtn_Close = GO.transform.Find("Root/UINode_Main/UIBtn_Close").GetComponent<Button>();
		UIBtn_SetUp = GO.transform.Find("Root/UINode_Main/UIBtn_SetUp").GetComponent<Button>();
		UITxt_AdsCount = GO.transform.Find("Root/UINode_Main/UINode_Ads/UITxt_AdsCount").GetComponent<LocalizeTextMeshProUGUI>();
		UINode_Ads = GO.transform.Find("Root/UINode_Main/UINode_Ads").GetComponent<RectTransform>();
		UINode_BlockPool = GO.transform.Find("Root/UINode_Main/UINode_BlockPool").GetComponent<RectTransform>();
		UINode_Rank = GO.transform.Find("Root/UINode_Main/UINode_Rank").GetComponent<RectTransform>();
		UINode_Content = GO.transform.Find("Root/UINode_Main/Display/UINode_Content").GetComponent<RectTransform>();
		UINode_Refer = GO.transform.Find("Root/UINode_Main/Display/UINode_Refer").GetComponent<RectTransform>();
		UINode_Block1 = GO.transform.Find("Root/UINode_Main/Display/UINode_BlockArea/UINode_Block1").GetComponent<RectTransform>();
		UINode_Block2 = GO.transform.Find("Root/UINode_Main/Display/UINode_BlockArea/UINode_Block2").GetComponent<RectTransform>();
		UINode_Block3 = GO.transform.Find("Root/UINode_Main/Display/UINode_BlockArea/UINode_Block3").GetComponent<RectTransform>();
		UINode_Area1 = GO.transform.Find("Root/UINode_Main/Display/UINode_BlockArea/DragArea/UINode_Area1").GetComponent<RectTransform>();
		UINode_Area2 = GO.transform.Find("Root/UINode_Main/Display/UINode_BlockArea/DragArea/UINode_Area2").GetComponent<RectTransform>();
		UINode_Area3 = GO.transform.Find("Root/UINode_Main/Display/UINode_BlockArea/DragArea/UINode_Area3").GetComponent<RectTransform>();
		UINode_FullShield = GO.transform.Find("Root/UINode_Main/Display/UINode_BlockArea/DragArea/UINode_FullShield").GetComponent<RectTransform>();
		UINode_BlockArea = GO.transform.Find("Root/UINode_Main/Display/UINode_BlockArea").GetComponent<RectTransform>();
		UINode_ContentSmail = GO.transform.Find("Root/UINode_Main/UINode_Down/UINode_ContentSmail").GetComponent<RectTransform>();
		UINode_Down = GO.transform.Find("Root/UINode_Main/UINode_Down").GetComponent<RectTransform>();
		UINode_BlockBornEffect = GO.transform.Find("Root/UINode_Main/UINode_BlockBornEffect").GetComponent<RectTransform>();
		UINode_TipPlay = GO.transform.Find("Root/UINode_Main/UINode_TipPlay").GetComponent<RectTransform>();
		UITxt_PType = GO.transform.Find("Root/UINode_Main/UIBtn_Debug/UITxt_PType").GetComponent<LocalizeTextMeshProUGUI>();
		UITxt_Complex = GO.transform.Find("Root/UINode_Main/UIBtn_Debug/UITxt_Complex").GetComponent<LocalizeTextMeshProUGUI>();
		UITxt_BlockPlan = GO.transform.Find("Root/UINode_Main/UIBtn_Debug/UITxt_BlockPlan").GetComponent<LocalizeTextMeshProUGUI>();
		UIBtn_Debug = GO.transform.Find("Root/UINode_Main/UIBtn_Debug").GetComponent<Button>();
		UINode_Main = GO.transform.Find("Root/UINode_Main").GetComponent<RectTransform>();
		UINode_Tip = GO.transform.Find("Root/UINode_Tip").GetComponent<RectTransform>();
		UITxt_BlockOrder = GO.transform.Find("Root/UINode_Debug/HLayout/UITxt_BlockOrder").GetComponent<LocalizeTextMeshProUGUI>();
		UITxt_Complexlnfo = GO.transform.Find("Root/UINode_Debug/UITxt_Complexlnfo").GetComponent<LocalizeTextMeshProUGUI>();
		UIBtn_Random = GO.transform.Find("Root/UINode_Debug/ScrollView/Viewport/Content/UIBtn_Random").GetComponent<Button>();
		UIBtn_Hard = GO.transform.Find("Root/UINode_Debug/ScrollView/Viewport/Content/UIBtn_Hard").GetComponent<Button>();
		UIBtn_ComplexityUp = GO.transform.Find("Root/UINode_Debug/ScrollView/Viewport/Content/UIBtn_ComplexityUp").GetComponent<Button>();
		UIBtn_ComplexityDown = GO.transform.Find("Root/UINode_Debug/ScrollView/Viewport/Content/UIBtn_ComplexityDown").GetComponent<Button>();
		UIBtn_MultiClear = GO.transform.Find("Root/UINode_Debug/ScrollView/Viewport/Content/UIBtn_MultiClear").GetComponent<Button>();
		UIBtn_ClearScreen = GO.transform.Find("Root/UINode_Debug/ScrollView/Viewport/Content/UIBtn_ClearScreen").GetComponent<Button>();
		UINode_Debug = GO.transform.Find("Root/UINode_Debug").GetComponent<RectTransform>();
		UINode_PutTip = GO.transform.Find("Root/UINode_PutTip").GetComponent<RectTransform>();
		UINode_finger1 = GO.transform.Find("Root/UINode_finger1").GetComponent<RectTransform>();
		UINode_finger2 = GO.transform.Find("Root/UINode_finger2").GetComponent<RectTransform>();
		UINode_TempFinger1 = GO.transform.Find("Root/UINode_Main/Display/UINode_Content/UINode_TempFinger1").GetComponent<RectTransform>();

    }
}
