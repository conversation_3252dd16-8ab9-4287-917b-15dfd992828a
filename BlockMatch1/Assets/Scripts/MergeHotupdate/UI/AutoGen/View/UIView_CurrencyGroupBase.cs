/******************************/
/*****自动生成的UIView界面代码，禁止手动修改*****/
/*****界面逻辑写在子类中*****/
/*****生成时间：2025-3-17 17:23:19*****/
/*****************************/

using Framework;
using UnityEngine.UI;
using DragonPlus;
using UnityEngine;

public class UIView_CurrencyGroupBase : UIViewBase
{
	protected Button UIBtn_ButtonAvatar;
	protected Image UIImg_Infinite;
	protected LocalizeTextMeshProUGUI UITxt_Count1;
	protected LocalizeTextMeshProUGUI UITxt_TimeText1;
	protected Image UIImg_BuyImage1;
	protected Button UIBtn_Live;
	protected LocalizeTextMeshProUGUI UITxt_Count2;
	protected Image UIImg_BuyImage2;
	protected Button UIBtn_Coin;
	protected LocalizeTextMeshProUGUI UITxt_Count3;
	protected Image UIImg_BuyImage3;
	protected Button UIBtn_Diamond;
	protected RectTransform UINode_Main;

    protected override void BindComponent()
    {
		UIBtn_ButtonAvatar = GO.transform.Find("Root/UINode_Main/TopNode/UIBtn_ButtonAvatar").GetComponent<Button>();
		UIImg_Infinite = GO.transform.Find("Root/UINode_Main/TopNode/UIBtn_Live/Icon/UIImg_Infinite").GetComponent<Image>();
		UITxt_Count1 = GO.transform.Find("Root/UINode_Main/TopNode/UIBtn_Live/UITxt_Count1").GetComponent<LocalizeTextMeshProUGUI>();
		UITxt_TimeText1 = GO.transform.Find("Root/UINode_Main/TopNode/UIBtn_Live/UITxt_TimeText1").GetComponent<LocalizeTextMeshProUGUI>();
		UIImg_BuyImage1 = GO.transform.Find("Root/UINode_Main/TopNode/UIBtn_Live/UIImg_BuyImage1").GetComponent<Image>();
		UIBtn_Live = GO.transform.Find("Root/UINode_Main/TopNode/UIBtn_Live").GetComponent<Button>();
		UITxt_Count2 = GO.transform.Find("Root/UINode_Main/TopNode/UIBtn_Coin/UITxt_Count2").GetComponent<LocalizeTextMeshProUGUI>();
		UIImg_BuyImage2 = GO.transform.Find("Root/UINode_Main/TopNode/UIBtn_Coin/UIImg_BuyImage2").GetComponent<Image>();
		UIBtn_Coin = GO.transform.Find("Root/UINode_Main/TopNode/UIBtn_Coin").GetComponent<Button>();
		UITxt_Count3 = GO.transform.Find("Root/UINode_Main/TopNode/UIBtn_Diamond/UITxt_Count3").GetComponent<LocalizeTextMeshProUGUI>();
		UIImg_BuyImage3 = GO.transform.Find("Root/UINode_Main/TopNode/UIBtn_Diamond/UIImg_BuyImage3").GetComponent<Image>();
		UIBtn_Diamond = GO.transform.Find("Root/UINode_Main/TopNode/UIBtn_Diamond").GetComponent<Button>();
		UINode_Main = GO.transform.Find("Root/UINode_Main").GetComponent<RectTransform>();

    }
}
