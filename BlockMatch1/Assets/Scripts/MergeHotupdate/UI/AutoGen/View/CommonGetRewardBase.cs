/******************************/
/*****自动生成的UIView界面代码，禁止手动修改*****/
/*****界面逻辑写在子类中*****/
/*****生成时间：2025-6-3 14:4:29*****/
/*****************************/

using Framework;
using UnityEngine.UI;
using UnityEngine;

public class CommonGetRewardBase : UIViewBase
{
	protected Image UIImg_Box;
	protected Image UIImg_BoxOpen;
	protected Image UIImg_Box_UIImg_BoxBottomIcon;
	protected Image UIImg_BoxBottomIcon;
	protected Image UIImg_BoxTopIcon;
	protected RectTransform UINode_BoxGroup;
	protected Image UIImg_BoxOpen_UINode_LevelBox;
	protected Image UIImg_BoxOpen2;
	protected Image UIImg_BoxBottomIcon_UINode_LevelBox;
	protected RectTransform UINode_LevelBox;

    protected override void BindComponent()
    {
		UIImg_Box = GO.transform.Find("UINode_BoxGroup/UIImg_BoxOpen/UIImg_Box").GetComponent<Image>();
		UIImg_BoxOpen = GO.transform.Find("UINode_BoxGroup/UIImg_BoxOpen").GetComponent<Image>();
		UIImg_Box_UIImg_BoxBottomIcon = GO.transform.Find("UINode_BoxGroup/UIImg_BoxBottomIcon/UIImg_Box").GetComponent<Image>();
		UIImg_BoxBottomIcon = GO.transform.Find("UINode_BoxGroup/UIImg_BoxBottomIcon").GetComponent<Image>();
		UIImg_BoxTopIcon = GO.transform.Find("UINode_BoxGroup/UIImg_BoxTopIcon").GetComponent<Image>();
		UINode_BoxGroup = GO.transform.Find("UINode_BoxGroup").GetComponent<RectTransform>();
		UIImg_BoxOpen_UINode_LevelBox = GO.transform.Find("UINode_LevelBox/UIImg_BoxOpen").GetComponent<Image>();
		UIImg_BoxOpen2 = GO.transform.Find("UINode_LevelBox/UIImg_BoxOpen2").GetComponent<Image>();
		UIImg_BoxBottomIcon_UINode_LevelBox = GO.transform.Find("UINode_LevelBox/UIImg_BoxBottomIcon").GetComponent<Image>();
		UINode_LevelBox = GO.transform.Find("UINode_LevelBox").GetComponent<RectTransform>();

    }
}
