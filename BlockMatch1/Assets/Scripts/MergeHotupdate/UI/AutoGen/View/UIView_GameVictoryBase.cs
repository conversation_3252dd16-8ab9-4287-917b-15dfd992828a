/******************************/
/*****自动生成的UIView界面代码，禁止手动修改*****/
/*****界面逻辑写在子类中*****/
/*****生成时间：2025-3-22 14:44:14*****/
/*****************************/

using Framework;
using DragonPlus;
using UnityEngine.UI;
using UnityEngine;

public class UIView_GameVictoryBase : UIViewBase
{
	protected LocalizeTextMeshProUGUI UITxt_Level_Pass;
	protected LocalizeTextMeshProUGUI UITxt_RewardCount;
	protected LocalizeTextMeshProUGUI UITxt_RewardCount_Coin;
	protected Button UIBtn_Receive;
	protected Button UIBtn_Double;
	protected RectTransform UINode_Passed;
	protected Button UIBtn_Close;
	protected Button UIBtn_PlayOn;
	protected LocalizeTextMeshProUGUI UITxt_ReviveCoin;
	protected Button UIBtn_Receive_Ns;
	protected RectTransform UINode_Spaces;
	protected LocalizeTextMeshProUGUI UITxt_TargetDes;
	protected LocalizeTextMeshProUGUI UITxt_Level_Fail;
	protected LocalizeTextMeshProUGUI UITxt_ProgressEx;
	protected LocalizeTextMeshProUGUI UITxt_Progress;
	protected LocalizeTextMeshProUGUI UITxt_End;
	protected Slider UISlider_ScoreTarget;
	protected HorizontalLayoutGroup UILayoutH_Gems;
	protected RectTransform UINode_GemTarget;
	protected Button UIBtn_Again;
	protected Button UIBtn_Home;
	protected RectTransform UINode_Failed;

    protected override void BindComponent()
    {
		UITxt_Level_Pass = GO.transform.Find("Root/UINode_Passed/Level/UITxt_Level_Pass").GetComponent<LocalizeTextMeshProUGUI>();
		UITxt_RewardCount = GO.transform.Find("Root/UINode_Passed/Level/UICe_LevelTip/Diamond/UITxt_RewardCount").GetComponent<LocalizeTextMeshProUGUI>();
		UITxt_RewardCount_Coin = GO.transform.Find("Root/UINode_Passed/Level/UICe_LevelTip/Coin/UITxt_RewardCount").GetComponent<LocalizeTextMeshProUGUI>();
		UIBtn_Receive = GO.transform.Find("Root/UINode_Passed/UIDown/UIBtn_Receive").GetComponent<Button>();
		UIBtn_Double = GO.transform.Find("Root/UINode_Passed/UIDown/UIBtn_Double").GetComponent<Button>();
		UINode_Passed = GO.transform.Find("Root/UINode_Passed").GetComponent<RectTransform>();
		UIBtn_Close = GO.transform.Find("Root/UINode_Spaces/UIPop_Smail/UIBtn_Close").GetComponent<Button>();
		UIBtn_PlayOn = GO.transform.Find("Root/UINode_Spaces/Layout/UIBtn_PlayOn").GetComponent<Button>();
		UITxt_ReviveCoin = GO.transform.Find("Root/UINode_Spaces/Layout/UIBtn_Receive_Ns/Coin/UITxt_ReviveCoin").GetComponent<LocalizeTextMeshProUGUI>();
		UIBtn_Receive_Ns = GO.transform.Find("Root/UINode_Spaces/Layout/UIBtn_Receive_Ns").GetComponent<Button>();
		UINode_Spaces = GO.transform.Find("Root/UINode_Spaces").GetComponent<RectTransform>();
		UITxt_TargetDes = GO.transform.Find("Root/UINode_Failed/Level/UITxt_TargetDes").GetComponent<LocalizeTextMeshProUGUI>();
		UITxt_Level_Fail = GO.transform.Find("Root/UINode_Failed/Level/UITxt_Level_Fail").GetComponent<LocalizeTextMeshProUGUI>();
		UITxt_ProgressEx = GO.transform.Find("Root/UINode_Failed/Level/UISlider_ScoreTarget/Fill Area/UITxt_ProgressEx").GetComponent<LocalizeTextMeshProUGUI>();
		UITxt_Progress = GO.transform.Find("Root/UINode_Failed/Level/UISlider_ScoreTarget/Fill Area/Progress/Icon/UITxt_Progress").GetComponent<LocalizeTextMeshProUGUI>();
		UITxt_End = GO.transform.Find("Root/UINode_Failed/Level/UISlider_ScoreTarget/End/Icon/UITxt_End").GetComponent<LocalizeTextMeshProUGUI>();
		UISlider_ScoreTarget = GO.transform.Find("Root/UINode_Failed/Level/UISlider_ScoreTarget").GetComponent<Slider>();
		UILayoutH_Gems = GO.transform.Find("Root/UINode_Failed/Level/UINode_GemTarget/Tip/UILayoutH_Gems").GetComponent<HorizontalLayoutGroup>();
		UINode_GemTarget = GO.transform.Find("Root/UINode_Failed/Level/UINode_GemTarget").GetComponent<RectTransform>();
		UIBtn_Again = GO.transform.Find("Root/UINode_Failed/UIDown/UIBtn_Again").GetComponent<Button>();
		UIBtn_Home = GO.transform.Find("Root/UINode_Failed/UIDown/UIBtn_Home").GetComponent<Button>();
		UINode_Failed = GO.transform.Find("Root/UINode_Failed").GetComponent<RectTransform>();

    }
}
