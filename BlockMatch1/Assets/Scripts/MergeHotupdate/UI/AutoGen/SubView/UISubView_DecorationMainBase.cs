/******************************/
/*****自动生成的UISubView界面代码，禁止手动修改*****/
/*****界面逻辑写在子类中*****/
/*****生成时间：2025-3-12 16:11:15*****/
/*****************************/

using Framework;
using DragonPlus;
using UnityEngine.UI;
using UnityEngine;

public class UISubView_DecorationMainBase : UISubViewBase
{
	protected LocalizeTextMeshProUGUI UITxt_Count3;
	protected Image UIImg_BuyImage3;
	protected Button UIBtn_Diamond;
	protected LocalizeTextMeshProUGUI UITxt_DownLoadLabel;
	protected Slider UISlider_DownLoadSlider;
	protected RectTransform UINode_BoxIconClose;
	protected RectTransform UINode_BoxIconOpen;
	protected Button UIBtn_Box;
	protected RectTransform UINode_par1;
	protected RectTransform UINode_par2;
	protected RectTransform UINode_Form;
	protected RectTransform UINode_DecorationCurrency;
	protected LocalizeTextMeshProUGUI UITxt_Label;
	protected LocalizeTextMeshProUGUI UITxt_Progress;
	protected LocalizeTextMeshProUGUI UITxt_Loading;
	protected Slider UISlider;
	protected Button UIBtn_FullScreenClose;
	protected RectTransform UINode_SafeArea;
	protected RectTransform UINode_PlaceBtnClone;
	protected Button UIBtn_Back;
	protected Button UIBtn_CloseSelectButton;
	protected HorizontalLayoutGroup UILayoutH_FurnituresLayout;
	protected Button UIBtn_SureSelectButton;
	protected Button UIBtn_ADSButton;
	protected Button UIBtn_ADSLoadingButton;
	protected Button UIBtn_ADSButtonBottomLong;
	protected Button UIBtn_ADSLoadingButtonBottomLong;
	protected LocalizeTextMeshProUGUI UITxt_DecorationText;
	protected LocalizeTextMeshProUGUI UITxt_RewardText;
	protected LocalizeTextMeshProUGUI UITxt_BottomDecorationText;
	protected RectTransform UINode_SelectUI;
	protected RectTransform UINode_FurniturePointButton;
	protected Button UIBtn_RoomList;
	protected Button UIBtn_CloseButton;
	protected RectTransform UINode_ScreenOrigin;
	protected RectTransform UINode_Main;

    protected override void BindComponent()
    {
		UITxt_Count3 = GO.transform.Find("UINode_Main/UINode_DecorationCurrency/UIBtn_Diamond/UITxt_Count3").GetComponent<LocalizeTextMeshProUGUI>();
		UIImg_BuyImage3 = GO.transform.Find("UINode_Main/UINode_DecorationCurrency/UIBtn_Diamond/UIImg_BuyImage3").GetComponent<Image>();
		UIBtn_Diamond = GO.transform.Find("UINode_Main/UINode_DecorationCurrency/UIBtn_Diamond").GetComponent<Button>();
		UITxt_DownLoadLabel = GO.transform.Find("UINode_Main/UINode_DecorationCurrency/UISlider_DownLoadSlider/UITxt_DownLoadLabel").GetComponent<LocalizeTextMeshProUGUI>();
		UISlider_DownLoadSlider = GO.transform.Find("UINode_Main/UINode_DecorationCurrency/UISlider_DownLoadSlider").GetComponent<Slider>();
		UINode_BoxIconClose = GO.transform.Find("UINode_Main/UINode_DecorationCurrency/UIBtn_Box/UINode_BoxIconClose").GetComponent<RectTransform>();
		UINode_BoxIconOpen = GO.transform.Find("UINode_Main/UINode_DecorationCurrency/UIBtn_Box/UINode_BoxIconOpen").GetComponent<RectTransform>();
		UIBtn_Box = GO.transform.Find("UINode_Main/UINode_DecorationCurrency/UIBtn_Box").GetComponent<Button>();
		UINode_par1 = GO.transform.Find("UINode_Main/UINode_DecorationCurrency/UINode_Form/Root/ButtonForm/GameObject/UINode_par1").GetComponent<RectTransform>();
		UINode_par2 = GO.transform.Find("UINode_Main/UINode_DecorationCurrency/UINode_Form/Root/ButtonForm/GameObject/UINode_par2").GetComponent<RectTransform>();
		UINode_Form = GO.transform.Find("UINode_Main/UINode_DecorationCurrency/UINode_Form").GetComponent<RectTransform>();
		UINode_DecorationCurrency = GO.transform.Find("UINode_Main/UINode_DecorationCurrency").GetComponent<RectTransform>();
		UITxt_Label = GO.transform.Find("UINode_Main/UIBtn_FullScreenClose/UISlider/UITxt_Label").GetComponent<LocalizeTextMeshProUGUI>();
		UITxt_Progress = GO.transform.Find("UINode_Main/UIBtn_FullScreenClose/UISlider/UITxt_Progress").GetComponent<LocalizeTextMeshProUGUI>();
		UITxt_Loading = GO.transform.Find("UINode_Main/UIBtn_FullScreenClose/UISlider/UITxt_Loading").GetComponent<LocalizeTextMeshProUGUI>();
		UISlider = GO.transform.Find("UINode_Main/UIBtn_FullScreenClose/UISlider").GetComponent<Slider>();
		UIBtn_FullScreenClose = GO.transform.Find("UINode_Main/UIBtn_FullScreenClose").GetComponent<Button>();
		UINode_SafeArea = GO.transform.Find("UINode_Main/UINode_SafeArea").GetComponent<RectTransform>();
		UINode_PlaceBtnClone = GO.transform.Find("UINode_Main/UINode_PlaceBtnClone").GetComponent<RectTransform>();
		UIBtn_Back = GO.transform.Find("UINode_Main/UIBtn_Back").GetComponent<Button>();
		UIBtn_CloseSelectButton = GO.transform.Find("UINode_Main/UINode_SelectUI/SelectFurnituresGroup/CloseSelectButton/UIBtn_CloseSelectButton").GetComponent<Button>();
		UILayoutH_FurnituresLayout = GO.transform.Find("UINode_Main/UINode_SelectUI/SelectFurnituresGroup/UILayoutH_FurnituresLayout").GetComponent<HorizontalLayoutGroup>();
		UIBtn_SureSelectButton = GO.transform.Find("UINode_Main/UINode_SelectUI/SelectFurnituresGroup/SureSelectButton/UIBtn_SureSelectButton").GetComponent<Button>();
		UIBtn_ADSButton = GO.transform.Find("UINode_Main/UINode_SelectUI/SelectFurnituresGroup/ADSButton/UIBtn_ADSButton").GetComponent<Button>();
		UIBtn_ADSLoadingButton = GO.transform.Find("UINode_Main/UINode_SelectUI/SelectFurnituresGroup/ADSLoadingButton/UIBtn_ADSLoadingButton").GetComponent<Button>();
		UIBtn_ADSButtonBottomLong = GO.transform.Find("UINode_Main/UINode_SelectUI/SelectFurnituresGroup/ADSButtonBottomLong/UIBtn_ADSButtonBottomLong").GetComponent<Button>();
		UIBtn_ADSLoadingButtonBottomLong = GO.transform.Find("UINode_Main/UINode_SelectUI/SelectFurnituresGroup/ADSLoadingButtonBottomLong/UIBtn_ADSLoadingButtonBottomLong").GetComponent<Button>();
		UITxt_DecorationText = GO.transform.Find("UINode_Main/UINode_SelectUI/DecorationGroup/UITxt_DecorationText").GetComponent<LocalizeTextMeshProUGUI>();
		UITxt_RewardText = GO.transform.Find("UINode_Main/UINode_SelectUI/DecorationGroup/UITxt_RewardText").GetComponent<LocalizeTextMeshProUGUI>();
		UITxt_BottomDecorationText = GO.transform.Find("UINode_Main/UINode_SelectUI/DecBottomGroup/UITxt_BottomDecorationText").GetComponent<LocalizeTextMeshProUGUI>();
		UINode_SelectUI = GO.transform.Find("UINode_Main/UINode_SelectUI").GetComponent<RectTransform>();
		UINode_FurniturePointButton = GO.transform.Find("UINode_Main/UINode_ScreenOrigin/UINode_FurniturePointButton").GetComponent<RectTransform>();
		UIBtn_RoomList = GO.transform.Find("UINode_Main/UINode_ScreenOrigin/UIBtn_RoomList").GetComponent<Button>();
		UIBtn_CloseButton = GO.transform.Find("UINode_Main/UINode_ScreenOrigin/UIBtn_CloseButton").GetComponent<Button>();
		UINode_ScreenOrigin = GO.transform.Find("UINode_Main/UINode_ScreenOrigin").GetComponent<RectTransform>();
		UINode_Main = GO.transform.Find("UINode_Main").GetComponent<RectTransform>();

    }
}
