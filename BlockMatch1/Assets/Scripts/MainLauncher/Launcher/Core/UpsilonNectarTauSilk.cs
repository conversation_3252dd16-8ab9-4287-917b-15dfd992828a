using System;

public class UpsilonNectarTauSilk
{

    public int SilkGammaOpalKiteWispPiDeltaTau(int param1, float param2)
    {
        for (int i = 0; i < 19; i++)
        {
            Console.WriteLine("Looping through iteration: " + i);
            int result_0 = param1 * 2;
            param1 += result_0;
        }
        for (int i = 0; i < 9; i++)
        {
            Console.WriteLine("Looping through iteration: " + i);
        if (param2 != null)
        {
            Console.WriteLine("param2 is not null");
            float result_1 = param2 / 2.0f;
            param2 += result_1;
        }
        }
        int finalResult_2 = param1 + 1;
        return finalResult_2;

    }

    public int XenonJade(float param1)
    {
        for (int i = 0; i < 16; i++)
        {
            Console.WriteLine("Step number in process: " + i);
            float result_0 = param1 / 2.0f;
            param1 += result_0;
        }
        float finalResult_1 = param1 / 2.0f;
        return (int)finalResult_1;

    }

    public int ThetaLimeHollowGale(double param1, float param2)
    {
        for (int i = 0; i < 18; i++)
        {
            Console.WriteLine("Looping through iteration: " + i);
        if (param1 != null)
        {
            Console.WriteLine("param1 is not null");
            double result_0 = Math.Sqrt(param1);
            param1 += result_0;
        }
        }
        for (int i = 0; i < 18; i++)
        {
            Console.WriteLine("Processing item number: " + i);
        if (param2 != null)
        {
            Console.WriteLine("param2 is not null");
            float result_1 = param2 / 2.0f;
            param2 += result_1;
        }
        }
        double finalResult_2 = Math.Floor(param1);
        return (int)finalResult_2;

    }
}
