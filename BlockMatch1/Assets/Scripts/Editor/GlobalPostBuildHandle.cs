using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEditor.Build;
using UnityEditor.Build.Reporting;
#if UNITY_ANDROID
using UnityEditor.Android;
#endif
using System.Text.RegularExpressions;
using UnityEditor;
using System.Xml.Linq;

public class GlobalPostBuildHandle : IPostprocessBuildWithReport
#if UNITY_ANDROID
    , IPostGenerateGradleAndroidProject
#endif
{
    public int callbackOrder => 6;

    private static readonly Regex TokenDistributionUrl = new Regex(".*distributionUrl.*");
    private static readonly Regex TokenEnableUnCom = new Regex(".*enableUncompressedNativeLibs.*");

    private static bool ReplaceStringInFile(string path, Regex regexToMatch, string replacement)
    {
        if (!File.Exists(path)) return false;

        var lines = File.ReadAllLines(path);
        for (var i = 0; i < lines.Length; i++)
        {
            if (regexToMatch.IsMatch(lines[i]))
            {
                lines[i] = replacement;
                File.WriteAllLines(path, lines);
                return true;
            }
        }

        return false;
    }

    public void OnPostprocessBuild(BuildReport report)
    {
#if UNITY_ANDROID
        var gradleWrapperPropertiesPath = Path.Combine(report.summary.outputPath, "gradle/wrapper/gradle-wrapper.properties");
        Debug.Log($"GlobalPostBuildHandle.OnPostprocessBuild--{gradleWrapperPropertiesPath}");
        if (File.Exists(gradleWrapperPropertiesPath))
        {
            var newDistributionUrl = "distributionUrl=https\\://services.gradle.org/distributions/gradle-8.0-all.zip";
            Debug.Log($"GlobalPostBuildHandle.OnPostprocessBuild--gradleWrapperPropertiesPath is Exist");
            if (ReplaceStringInFile(gradleWrapperPropertiesPath, TokenDistributionUrl, newDistributionUrl))
            {
                Debug.Log("GlobalPostBuildHandle.OnPostprocessBuild,Distribution url set to " + newDistributionUrl);
            }
            else
            {
                Debug.LogError("GlobalPostBuildHandle.OnPostprocessBuild,Failed to set distribution URL");
            }

        }
#endif
    }

#if UNITY_ANDROID
    public void OnPostGenerateGradleAndroidProject(string path)
    {
        var gradleWrapperPropertiesPath = Path.Combine(path, "../gradle/wrapper/gradle-wrapper.properties");
        Debug.Log($"GlobalPostBuildHandle.OnPostGenerateGradleAndroidProject--{gradleWrapperPropertiesPath}");
        if (File.Exists(gradleWrapperPropertiesPath))
        {
            var newDistributionUrl = "distributionUrl=https\\://services.gradle.org/distributions/gradle-8.0-all.zip";
            Debug.Log($"GlobalPostBuildHandle.OnPostGenerateGradleAndroidProject--gradleWrapperPropertiesPath is Exist");
            if (ReplaceStringInFile(gradleWrapperPropertiesPath, TokenDistributionUrl, newDistributionUrl))
            {
                Debug.Log("GlobalPostBuildHandle.OnPostGenerateGradleAndroidProject,Distribution url set to " + newDistributionUrl);
            }
            else
            {
                Debug.LogError("GlobalPostBuildHandle.OnPostGenerateGradleAndroidProject,Failed to set distribution URL");
            }
        }

        var gradlePropertiesPath = Path.Combine(path, "../gradle.properties");
        Debug.Log($"GlobalPostBuildHandle.OnPostprocessBuild--{gradlePropertiesPath}");
        if (File.Exists(gradlePropertiesPath))
        {
            Debug.Log($"GlobalPostBuildHandle.OnPostprocessBuild--gradlePropertiesPath is Exist");
            if (ReplaceStringInFile(gradlePropertiesPath, TokenEnableUnCom, ""))
            {
                Debug.Log("GlobalPostBuildHandle.OnPostprocessBuild, enableUncompressedNativeLibs set to empty ");
            }
        }

        var theCurPath = path.Replace("\\", "/");
        var unityLibraryPath = $"{theCurPath}/../";
        if (Directory.Exists(unityLibraryPath))
        {//处理 AndroidManifest 的 package 属性
            var theFiles = Directory.GetFiles(unityLibraryPath, "AndroidManifest.xml", SearchOption.AllDirectories);
            XDocument manifest = new XDocument();
            for (int i = 0; i < theFiles.Length; i++)
            {
                var manifestPath = theFiles[i];

                try
                {
                    manifest = XDocument.Load(manifestPath);
                }
                catch (IOException e)
                {
                    Debug.LogWarning($"AndroidManifest.xml is missing. Try re-importing the plugin. error is {e}");
                    continue;
                }

                XElement elemManifest = manifest.Element("manifest");
                if (elemManifest != null)
                {
                    var attPackage = elemManifest.Attribute("package");
                    if (attPackage != null)
                    {
                        attPackage.Remove();
                        elemManifest.Save(manifestPath);
                    }
                }
            }
        }
    }
#endif
}
