{"tiles": [{"x": 0, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 0, "y": 2, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 0, "y": 3, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 0, "y": 4, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 0, "y": 5, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 0, "y": 6, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 0, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 1, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 2, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 2, "y": 3, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 2, "y": 4, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 2, "y": 5, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 2, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 3, "y": 3, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 3, "y": 5, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 3, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 4, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 4, "y": 3, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 4, "y": 4, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 4, "y": 5, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 4, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 5, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 6, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 6, "y": 2, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 6, "y": 4, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 6, "y": 6, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}], "targets": {"targetType": "collection", "targetItems": [{"type": 301, "count": 5}, {"type": 401, "count": 27}, {"type": 302, "count": 5}]}}