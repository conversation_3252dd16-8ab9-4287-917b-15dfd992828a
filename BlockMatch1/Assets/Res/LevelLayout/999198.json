{"tiles": [{"x": 0, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 0, "y": 1, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 0, "y": 2, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 0, "y": 3, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 0, "y": 4, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 0, "y": 5, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 1, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 1, "y": 5, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 2, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 2, "y": 2, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 2, "y": 4, "baseType": "gem", "color": 0, "gem": 2, "overlays": [{"id": 502, "layers": 1}]}, {"x": 2, "y": 5, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 2, "y": 6, "baseType": "gem", "color": 0, "gem": 2, "overlays": [{"id": 502, "layers": 1}]}, {"x": 2, "y": 7, "baseType": "gem", "color": 0, "gem": 2, "overlays": [{"id": 502, "layers": 1}]}, {"x": 3, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 3, "y": 5, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 3, "y": 7, "baseType": "gem", "color": 0, "gem": 2, "overlays": [{"id": 502, "layers": 1}]}, {"x": 4, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 4, "y": 2, "baseType": "gem", "color": 0, "gem": 2, "overlays": [{"id": 502, "layers": 1}]}, {"x": 4, "y": 4, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 4, "y": 7, "baseType": "gem", "color": 0, "gem": 2, "overlays": [{"id": 502, "layers": 1}]}, {"x": 5, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 5, "y": 1, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 5, "y": 2, "baseType": "gem", "color": 0, "gem": 2, "overlays": [{"id": 502, "layers": 1}]}, {"x": 5, "y": 3, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 5, "y": 7, "baseType": "gem", "color": 0, "gem": 2, "overlays": [{"id": 502, "layers": 1}]}, {"x": 6, "y": 2, "baseType": "gem", "color": 0, "gem": 2, "overlays": [{"id": 502, "layers": 1}]}, {"x": 6, "y": 7, "baseType": "gem", "color": 0, "gem": 2, "overlays": [{"id": 502, "layers": 1}]}, {"x": 7, "y": 2, "baseType": "gem", "color": 0, "gem": 2, "overlays": [{"id": 502, "layers": 1}]}, {"x": 7, "y": 3, "baseType": "gem", "color": 0, "gem": 2, "overlays": [{"id": 502, "layers": 1}]}, {"x": 7, "y": 4, "baseType": "gem", "color": 0, "gem": 2, "overlays": [{"id": 502, "layers": 1}]}, {"x": 7, "y": 5, "baseType": "gem", "color": 0, "gem": 2, "overlays": [{"id": 502, "layers": 1}]}, {"x": 7, "y": 6, "baseType": "gem", "color": 0, "gem": 2, "overlays": [{"id": 502, "layers": 1}]}, {"x": 7, "y": 7, "baseType": "gem", "color": 0, "gem": 2, "overlays": [{"id": 502, "layers": 1}]}], "targets": {"targetType": "collection", "targetScore": 0, "targetItems": [{"type": 2, "count": 16}, {"type": 301, "count": 16}, {"type": 302, "count": 2}]}}