{"tiles": [{"x": 0, "y": 3, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 0, "y": 4, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 1, "y": 3, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 1, "y": 4, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 2, "y": 3, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 2, "y": 4, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 3, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 3, "y": 1, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 3, "y": 2, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 3, "y": 3, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 3, "y": 4, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 3, "y": 5, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 3, "y": 6, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 4, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 4, "y": 1, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 4, "y": 2, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 4, "y": 3, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 4, "y": 4, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 4, "y": 5, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 5, "y": 3, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 5, "y": 4, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}, {"x": 6, "y": 3, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 3}], "targets": {"targetType": "collection", "targetScore": 0, "targetItems": [{"type": 301, "count": 18}, {"type": 401, "count": 28}]}}