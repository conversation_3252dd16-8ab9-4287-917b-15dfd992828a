[{"id": 1, "experienceType": 1, "complextiyRange": [0, 100], "complexityIncreaseWeight": 70, "randomWeight": 30}, {"id": 2, "experienceType": 1, "complextiyRange": [100, 200], "complexityIncreaseWeight": 70, "randomWeight": 30}, {"id": 3, "experienceType": 1, "complextiyRange": [200, 350], "complexityIncreaseWeight": 50, "complexityDecreaseWeight": 50}, {"id": 4, "experienceType": 1, "complextiyRange": [350, 400], "complexityDecreaseWeight": 100}, {"id": 5, "experienceType": 1, "complextiyRange": [400, 500], "complexityDecreaseWeight": 100}, {"id": 6, "experienceType": 1, "complextiyRange": [500, 600], "complexityDecreaseWeight": 100}, {"id": 7, "experienceType": 1, "complextiyRange": [600, 99999], "complexityDecreaseWeight": 100}, {"id": 8, "experienceType": 2, "complextiyRange": [0, 100], "complexityIncreaseWeight": 80, "randomWeight": 20}, {"id": 9, "experienceType": 2, "complextiyRange": [100, 200], "complexityIncreaseWeight": 80, "randomWeight": 20}, {"id": 10, "experienceType": 2, "complextiyRange": [200, 350], "complexityIncreaseWeight": 70, "randomWeight": 30}, {"id": 11, "experienceType": 2, "complextiyRange": [350, 400], "complexityIncreaseWeight": 30, "randomWeight": 70}, {"id": 12, "experienceType": 2, "complextiyRange": [400, 500], "randomWeight": 100}, {"id": 13, "experienceType": 2, "complextiyRange": [500, 600], "complexityDecreaseWeight": 30, "randomWeight": 70}, {"id": 14, "experienceType": 2, "complextiyRange": [600, 99999], "complexityDecreaseWeight": 30, "randomWeight": 70}, {"id": 15, "experienceType": 3, "complextiyRange": [0, 100], "complexityIncreaseWeight": 80, "randomWeight": 20}, {"id": 16, "experienceType": 3, "complextiyRange": [100, 200], "complexityIncreaseWeight": 80, "randomWeight": 20}, {"id": 17, "experienceType": 3, "complextiyRange": [200, 350], "complexityIncreaseWeight": 80, "randomWeight": 20}, {"id": 18, "experienceType": 3, "complextiyRange": [350, 400], "complexityIncreaseWeight": 30, "difficultyWeight": 70}, {"id": 19, "experienceType": 3, "complextiyRange": [400, 500], "complexityIncreaseWeight": 30, "difficultyWeight": 70}, {"id": 20, "experienceType": 3, "complextiyRange": [500, 600], "randomWeight": 70, "difficultyWeight": 30}, {"id": 21, "experienceType": 3, "complextiyRange": [600, 99999], "complexityDecreaseWeight": 30, "randomWeight": 50, "difficultyWeight": 20}]