[{"id": 1, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {CurrentScore}<=2000 and {BestScores} == 0", "skills": [10, 16, 29, 1, 2, 3, 4, 5], "weights": [20, 40, 0, 0, 0, 0, 0, 20]}, {"id": 2, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {CurrentScore}<=2000 and {BestScores} == 0", "skills": [10, 17, 29, 1, 2, 3, 4, 5], "weights": [20, 40, 0, 0, 20, 0, 0, 20]}, {"id": 3, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {CurrentScore}<=2000 and {BestScores} == 0", "skills": [10, 18, 29, 1, 2, 3, 4, 5], "weights": [20, 10, 0, 0, 40, 0, 0, 10]}, {"id": 4, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {CurrentScore}<=2000 and {BestScores} == 0", "skills": [10, 20, 29, 1, 2, 3, 4, 5], "weights": [0, 0, 0, 40, 50, 0, 0, 0]}, {"id": 5, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {CurrentScore}<=2000 and {BestScores} == 0", "skills": [10, 21, 29, 1, 2, 3, 4, 5], "weights": [0, 0, 0, 20, 60, 0, 0, 0]}, {"id": 6, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {CurrentScore}>2000 and {CurrentScore}<=4000 and {BestScores} == 0", "skills": [10, 16, 32, 1, 2, 3, 4, 5], "weights": [20, 40, 0, 0, 0, 0, 0, 20]}, {"id": 7, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {CurrentScore}>2000 and {CurrentScore}<=4000 and {BestScores} == 0", "skills": [10, 17, 32, 1, 2, 3, 4, 5], "weights": [20, 40, 0, 0, 20, 0, 0, 20]}, {"id": 8, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {CurrentScore}<=2000 and {CurrentScore}<=4000 and {BestScores} == 0", "skills": [10, 18, 32, 1, 2, 3, 4, 5], "weights": [20, 10, 0, 0, 30, 0, 0, 10]}, {"id": 9, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {CurrentScore}<=2000 and {CurrentScore}<=4000 and {BestScores} == 0", "skills": [11, 20, 32, 1, 2, 3, 4, 5], "weights": [0, 0, 0, 40, 50, 0, 0, 0]}, {"id": 10, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {CurrentScore}<=2000 and {CurrentScore}<=4000 and {BestScores} == 0", "skills": [11, 21, 32, 1, 2, 3, 4, 5], "weights": [0, 0, 0, 20, 60, 0, 0, 0]}, {"id": 11, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {CurrentScore}>4000 and {CurrentScore}<= 6000 and {BestScores} == 0", "skills": [11, 18, 34, 1, 2, 3, 4, 5], "weights": [0, 40, 0, 0, 0, 0, 0, 10]}, {"id": 12, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {CurrentScore}>4000 and {CurrentScore}<=6000 and {BestScores} == 0", "skills": [11, 20, 34, 1, 2, 3, 4, 5], "weights": [0, 40, 0, 0, 5, 0, 0, 10]}, {"id": 13, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {CurrentScore}>4000 and {CurrentScore}<=6000 and {BestScores} == 0", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [5, 20, 10, 5, 10, 0, 0, 0]}, {"id": 14, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {CurrentScore}>4000 and {CurrentScore}<=6000 and {BestScores} == 0", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [5, 10, 15, 5, 10, 0, 10, 0]}, {"id": 15, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {CurrentScore}>4000 and {CurrentScore}<=6000 and {BestScores} == 0", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [5, 10, 10, 5, 10, 0, 10, 0]}, {"id": 16, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {CurrentScore}>6000 and {CurrentScore}<= 99999999 and {BestScores} == 0", "skills": [11, 22, 34, 1, 2, 3, 4, 5], "weights": [0, 40, 0, 0, 0, 0, 0, 10]}, {"id": 17, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {CurrentScore}>6000 and {CurrentScore}<= 99999999 and {BestScores} == 0", "skills": [12, 22, 34, 1, 2, 3, 4, 5], "weights": [0, 40, 0, 0, 5, 0, 0, 10]}, {"id": 18, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {CurrentScore}>6000 and {CurrentScore}<= 99999999 and {BestScores} == 0", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [5, 20, 10, 5, 10, 0, 0, 0]}, {"id": 19, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {CurrentScore}>6000 and {CurrentScore}<= 99999999 and {BestScores} == 0", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [5, 10, 15, 5, 10, 0, 10, 0]}, {"id": 20, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {CurrentScore}>6000 and {CurrentScore}<= 99999999 and {BestScores} == 0", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 5, 20, 5, 10, 5, 5, 0]}, {"id": 21, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {CurrentScore}<=2000 and {BestScores} > 0  and {CurrentScore}<={BestScores}", "skills": [10, 16, 29, 1, 2, 3, 4, 5], "weights": [20, 40, 0, 0, 0, 0, 0, 20]}, {"id": 22, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {CurrentScore}<=2000 and {BestScores} > 0 and {CurrentScore}<={BestScores}", "skills": [10, 17, 29, 1, 2, 3, 4, 5], "weights": [20, 40, 0, 0, 20, 0, 0, 20]}, {"id": 23, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {CurrentScore}<=2000 and {BestScores} > 0 and {CurrentScore}<={BestScores}", "skills": [10, 18, 29, 1, 2, 3, 4, 5], "weights": [20, 10, 0, 0, 40, 0, 0, 10]}, {"id": 24, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {CurrentScore}<=2000 and {BestScores} > 0 and {CurrentScore}<={BestScores}", "skills": [10, 20, 29, 1, 2, 3, 4, 5], "weights": [0, 0, 0, 40, 50, 0, 0, 0]}, {"id": 25, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {CurrentScore}<=2000 and {BestScores} > 0 and {CurrentScore}<={BestScores}", "skills": [10, 21, 29, 1, 2, 3, 4, 5], "weights": [0, 0, 0, 20, 60, 0, 0, 0]}, {"id": 26, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {CurrentScore}>2000 and {CurrentScore}<=4000 and {BestScores} > 0 and {ExceedBestScores} <= 0", "skills": [10, 16, 32, 1, 2, 3, 4, 5], "weights": [20, 40, 0, 0, 0, 0, 0, 20]}, {"id": 27, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {CurrentScore}>2000 and {CurrentScore}<=4000 and {BestScores} > 0 and {ExceedBestScores} <= 0", "skills": [10, 17, 32, 1, 2, 3, 4, 5], "weights": [20, 40, 0, 0, 20, 0, 0, 20]}, {"id": 28, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {CurrentScore}>2000 and {CurrentScore}<=4000 and {BestScores} > 0 and {ExceedBestScores} <= 0", "skills": [10, 18, 32, 1, 2, 3, 4, 5], "weights": [20, 10, 0, 0, 30, 0, 0, 10]}, {"id": 29, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {CurrentScore}>2000 and {CurrentScore}<=4000 and {BestScores} > 0 and {ExceedBestScores} <= 0", "skills": [11, 20, 32, 1, 2, 3, 4, 5], "weights": [0, 0, 0, 40, 50, 0, 0, 0]}, {"id": 30, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {CurrentScore}>2000 and {CurrentScore}<=4000 and {BestScores} > 0 and {ExceedBestScores} <= 0", "skills": [11, 21, 32, 1, 2, 3, 4, 5], "weights": [0, 0, 0, 20, 60, 0, 0, 0]}, {"id": 31, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {CurrentScore}>4000 and {CurrentScore}<= 6000 and {BestScores} > 0 and {ExceedBestScores} <= 0", "skills": [11, 18, 34, 1, 2, 3, 4, 5], "weights": [0, 40, 0, 0, 0, 0, 0, 10]}, {"id": 32, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {CurrentScore}>4000 and {CurrentScore}<=6000 and {BestScores} > 0 and {ExceedBestScores} <= 0", "skills": [11, 20, 34, 1, 2, 3, 4, 5], "weights": [5, 40, 0, 0, 20, 0, 0, 20]}, {"id": 33, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {CurrentScore}>4000 and {CurrentScore}<=6000 and {BestScores} > 0 and {ExceedBestScores} <= 0", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 20, 10, 10, 0, 0, 0, 0]}, {"id": 34, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {CurrentScore}>4000 and {CurrentScore}<=6000 and {BestScores} > 0 and {ExceedBestScores} <= 0", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 5, 15, 5, 10, 0, 10, 0]}, {"id": 35, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {CurrentScore}>4000 and {CurrentScore}<=6000 and {BestScores} > 0 and {ExceedBestScores} <= 0", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 5, 20, 5, 10, 5, 5, 0]}, {"id": 36, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {CurrentScore}>6000 and {CurrentScore}<= 99999999 and {BestScores} > 0 and {ExceedBestScores} <= 0", "skills": [11, 22, 34, 1, 2, 3, 4, 5], "weights": [0, 40, 0, 0, 0, 0, 0, 10]}, {"id": 37, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {CurrentScore}>6000 and {CurrentScore}<= 99999999 and {BestScores} > 0 and {ExceedBestScores} <= 0", "skills": [12, 22, 34, 1, 2, 3, 4, 5], "weights": [5, 40, 10, 0, 10, 0, 0, 20]}, {"id": 38, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {CurrentScore}>6000 and {CurrentScore}<= 99999999 and {BestScores} > 0 and {ExceedBestScores} <= 0", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 20, 10, 10, 0, 0, 0, 0]}, {"id": 39, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {CurrentScore}>6000 and {CurrentScore}<= 99999999 and {BestScores} > 0 and {ExceedBestScores} <= 0", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 5, 15, 5, 10, 0, 10, 0]}, {"id": 40, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {CurrentScore}>6000 and {CurrentScore}<= 99999999 and {BestScores} > 0 and {ExceedBestScores} <= 0", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 5, 15, 5, 15, 0, 5, 0]}, {"id": 41, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {BestScores} > 0 and {ExceedBestScores} > 0 and {ExceedBestScores} <= 1000", "skills": [11, 18, 34, 1, 2, 3, 4, 5], "weights": [0, 40, 0, 0, 0, 0, 0, 10]}, {"id": 42, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {BestScores} > 0 and {ExceedBestScores} > 0 and {ExceedBestScores} <= 1000", "skills": [11, 20, 34, 1, 2, 3, 4, 5], "weights": [5, 40, 0, 0, 20, 0, 0, 20]}, {"id": 43, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {BestScores} > 0 and {ExceedBestScores} > 0 and {ExceedBestScores} <= 1000", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 20, 10, 10, 0, 0, 0, 0]}, {"id": 44, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {BestScores} > 0 and {ExceedBestScores} > 0 and {ExceedBestScores} <= 1000", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 5, 15, 5, 10, 0, 10, 0]}, {"id": 45, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {BestScores} > and {ExceedBestScores} > 0 and {ExceedBestScores} <= 1000", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 5, 20, 5, 10, 5, 5, 0]}, {"id": 46, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {BestScores} > 0 and {ExceedBestScores} >1000 and {ExceedBestScores} <= 2000", "skills": [11, 22, 34, 1, 2, 3, 4, 5], "weights": [5, 40, 0, 0, 0, 0, 0, 10]}, {"id": 47, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {BestScores} > 0 and {ExceedBestScores} >1000 and {ExceedBestScores} <= 2000", "skills": [12, 22, 34, 1, 2, 3, 4, 5], "weights": [5, 40, 10, 0, 10, 0, 0, 20]}, {"id": 48, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {BestScores} > 0 and {ExceedBestScores} >1000 and {ExceedBestScores} <= 2000", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 20, 10, 10, 0, 0, 0, 0]}, {"id": 49, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {BestScores} > 0 and {ExceedBestScores} >1000 and {ExceedBestScores} <= 2000", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 5, 15, 5, 10, 5, 10, 0]}, {"id": 50, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {BestScores} > 0 and {ExceedBestScores} >1000 and {ExceedBestScores} <= 2000", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 5, 15, 5, 10, 10, 5, 0]}, {"id": 51, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {BestScores} > 0 and {ExceedBestScores} >2000", "skills": [12, 22, 34, 1, 2, 3, 4, 5], "weights": [0, 40, 0, 0, 0, 0, 0, 10]}, {"id": 52, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {BestScores} > 0 and {ExceedBestScores} >2000", "skills": [12, 22, 34, 1, 2, 3, 4, 5], "weights": [5, 40, 0, 0, 20, 0, 0, 20]}, {"id": 53, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {BestScores} > 0 and {ExceedBestScores} >2000", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 20, 10, 10, 0, 0, 0, 0]}, {"id": 54, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {BestScores} > 0 and {ExceedBestScores} >2000", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 5, 15, 5, 10, 5, 10, 0]}, {"id": 55, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {BestScores} > 0 and {ExceedBestScores} >2000", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 5, 15, 5, 10, 10, 5, 0]}]