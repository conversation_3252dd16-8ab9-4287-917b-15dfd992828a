[{"id": 1, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {CurrentProgress} >=0 and {CurrentProgress}<=0.2", "skills": [8, 16, 29, 1, 2, 3, 4, 5], "weights": [0, 40, 0, 10, 0, 0, 0, 40]}, {"id": 2, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {CurrentProgress} >=0 and {CurrentProgress}<=0.2", "skills": [8, 17, 29, 1, 2, 3, 4, 5], "weights": [0, 40, 0, 10, 10, 0, 0, 40]}, {"id": 3, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {CurrentProgress} >=0 and {CurrentProgress}<=0.2", "skills": [8, 18, 29, 1, 2, 3, 4, 5], "weights": [0, 10, 0, 20, 40, 0, 0, 40]}, {"id": 4, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {CurrentProgress} >=0 and {CurrentProgress}<=0.2", "skills": [10, 20, 29, 1, 2, 3, 4, 5], "weights": [10, 10, 20, 40, 50, 0, 0, 30]}, {"id": 5, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {CurrentProgress} >=0 and {CurrentProgress}<=0.2", "skills": [10, 21, 29, 1, 2, 3, 4, 5], "weights": [10, 10, 30, 20, 60, 0, 0, 30]}, {"id": 6, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {CurrentProgress} >0.2 and {CurrentProgress}<=0.4", "skills": [10, 16, 32, 1, 2, 3, 4, 5], "weights": [0, 40, 0, 10, 0, 0, 0, 30]}, {"id": 7, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {CurrentProgress} >0.2 and {CurrentProgress}<=0.4", "skills": [10, 17, 32, 1, 2, 3, 4, 5], "weights": [0, 40, 0, 10, 0, 0, 0, 30]}, {"id": 8, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {CurrentProgress} >0.2 and {CurrentProgress}<=0.4", "skills": [10, 18, 32, 1, 2, 3, 4, 5], "weights": [0, 10, 0, 20, 40, 0, 0, 40]}, {"id": 9, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {CurrentProgress} >0.2 and {CurrentProgress}<=0.4", "skills": [11, 20, 32, 1, 2, 3, 4, 5], "weights": [10, 10, 20, 40, 50, 0, 0, 30]}, {"id": 10, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {CurrentProgress} >0.2 and {CurrentProgress}<=0.4", "skills": [11, 21, 32, 1, 2, 3, 4, 5], "weights": [10, 10, 30, 20, 60, 0, 0, 30]}, {"id": 11, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {CurrentProgress} >0.4 and {CurrentProgress}<=0.6", "skills": [11, 21, 34, 1, 2, 3, 4, 5], "weights": [10, 40, 5, 10, 0, 0, 5, 30]}, {"id": 12, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {CurrentProgress} >0.4 and {CurrentProgress}<=0.6", "skills": [11, 21, 34, 1, 2, 3, 4, 5], "weights": [10, 40, 10, 5, 5, 20, 10, 10]}, {"id": 13, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {CurrentProgress} >0.4 and {CurrentProgress}<=0.6", "skills": [11, 21, 34, 1, 2, 3, 4, 5], "weights": [0, 10, 0, 20, 40, 0, 0, 40]}, {"id": 14, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {CurrentProgress} >0.4 and {CurrentProgress}<=0.6", "skills": [11, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 10, 20, 40, 50, 0, 0, 30]}, {"id": 15, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {CurrentProgress} >0.4 and {CurrentProgress}<=0.6", "skills": [11, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 10, 30, 20, 60, 0, 0, 30]}, {"id": 16, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {CurrentProgress} >0.6 and {CurrentProgress}<=0.8", "skills": [11, 18, 34, 1, 2, 3, 4, 5], "weights": [0, 40, 5, 10, 0, 0, 5, 30]}, {"id": 17, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {CurrentProgress} >0.6 and {CurrentProgress}<=0.8", "skills": [11, 20, 34, 1, 2, 3, 4, 5], "weights": [0, 40, 10, 5, 5, 20, 10, 10]}, {"id": 18, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {CurrentProgress} >0.6 and {CurrentProgress}<=0.8", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 5, 20, 5, 10, 30, 10, 10]}, {"id": 19, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {CurrentProgress} >0.6 and {CurrentProgress}<=0.8", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 5, 20, 5, 10, 30, 10, 11]}, {"id": 20, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {CurrentProgress} >0.6 and {CurrentProgress}<=0.8", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 5, 20, 5, 10, 30, 10, 12]}, {"id": 21, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {CurrentProgress} >0.8 and {CurrentProgress}<=1", "skills": [11, 22, 34, 1, 2, 3, 4, 5], "weights": [0, 40, 5, 10, 10, 0, 5, 30]}, {"id": 22, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {CurrentProgress} >0.8 and {CurrentProgress}<=1", "skills": [12, 22, 34, 1, 2, 3, 4, 5], "weights": [0, 40, 10, 5, 5, 20, 10, 10]}, {"id": 23, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {CurrentProgress} >0.8 and {CurrentProgress}<=1", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [0, 5, 20, 5, 10, 30, 10, 10]}, {"id": 24, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {CurrentProgress} >0.8 and {CurrentProgress}<=1", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 5, 20, 5, 10, 30, 10, 11]}, {"id": 25, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {CurrentProgress} >0.8 and {CurrentProgress}<=1", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 5, 20, 5, 10, 30, 10, 12]}, {"id": 26, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {CurrentProgress} > 1 and {ExceedBestScores} > 0 and {ExceedBestScores} <= 1000", "skills": [11, 18, 34, 1, 2, 3, 4, 5], "weights": [0, 40, 5, 10, 10, 0, 5, 30]}, {"id": 27, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {CurrentProgress} > 1 and {ExceedBestScores} > 0 and {ExceedBestScores} <= 1000", "skills": [11, 20, 34, 1, 2, 3, 4, 5], "weights": [0, 40, 10, 5, 5, 20, 10, 10]}, {"id": 28, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {CurrentProgress} > 1 and {ExceedBestScores} > 0 and {ExceedBestScores} <= 1000", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [10, 5, 20, 5, 10, 40, 10, 10]}, {"id": 29, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {CurrentProgress} > 1 and {ExceedBestScores} > 0 and {ExceedBestScores} <= 1000", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [20, 5, 20, 5, 10, 40, 10, 11]}, {"id": 30, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {CurrentProgress} > 1 and {ExceedBestScores} > 0 and {ExceedBestScores} <= 1000", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [20, 5, 20, 5, 10, 40, 10, 12]}, {"id": 31, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {CurrentProgress} > 1 and {ExceedBestScores} > 1000 and {ExceedBestScores} <= 3000", "skills": [11, 22, 34, 1, 2, 3, 4, 5], "weights": [20, 40, 5, 10, 10, 0, 5, 30]}, {"id": 32, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {CurrentProgress} > 1 and {ExceedBestScores} > 1000 and {ExceedBestScores} <= 3000", "skills": [12, 22, 34, 1, 2, 3, 4, 5], "weights": [20, 40, 10, 5, 5, 20, 10, 10]}, {"id": 33, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {CurrentProgress} > 1 and {ExceedBestScores} > 1000 and {ExceedBestScores} <= 3000", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [20, 5, 20, 5, 10, 50, 10, 10]}, {"id": 34, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {CurrentProgress} > 1 and {ExceedBestScores} > 1000 and {ExceedBestScores} <= 3000", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [20, 5, 20, 5, 10, 50, 10, 11]}, {"id": 35, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {CurrentProgress} > 1 and {ExceedBestScores} > 1000 and {ExceedBestScores} <= 3000", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [20, 5, 20, 5, 10, 50, 10, 12]}, {"id": 36, "condition": "{CurrentComplexity}>=0 and {CurrentComplexity}<=200 and {CurrentProgress} > 1 and {ExceedBestScores} >3000 ", "skills": [12, 22, 34, 1, 2, 3, 4, 5], "weights": [20, 40, 5, 10, 10, 0, 5, 30]}, {"id": 37, "condition": "{CurrentComplexity}>200 and {CurrentComplexity}<=400 and {CurrentProgress} > 1 and {ExceedBestScores} >3000 ", "skills": [12, 22, 34, 1, 2, 3, 4, 5], "weights": [20, 40, 10, 5, 5, 20, 10, 10]}, {"id": 38, "condition": "{CurrentComplexity}>400 and {CurrentComplexity}<=550 and {CurrentProgress} > 1 and {ExceedBestScores} >3000 ", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [20, 5, 20, 5, 10, 60, 10, 10]}, {"id": 39, "condition": "{CurrentComplexity}>550 and {CurrentComplexity}<=680 and {CurrentProgress} > 1 and {ExceedBestScores} >3000 ", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [20, 5, 20, 5, 10, 60, 10, 11]}, {"id": 40, "condition": "{CurrentComplexity}>680 and {CurrentComplexity}<=9999 and {CurrentProgress} > 1 and {ExceedBestScores} >3000", "skills": [13, 22, 34, 1, 2, 3, 4, 5], "weights": [20, 5, 20, 5, 10, 60, 10, 12]}]