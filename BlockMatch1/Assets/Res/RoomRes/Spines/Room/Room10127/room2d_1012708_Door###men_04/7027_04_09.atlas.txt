
7027_04_09.png
size: 2048,512
format: RGBA8888
filter: Linear,Linear
repeat: none
4-1-curtainL
  rotate: false
  xy: 1114, 99
  size: 127, 199
  orig: 127, 199
  offset: 0, 0
  index: -1
4-1-curtainR
  rotate: false
  xy: 833, 272
  size: 103, 240
  orig: 103, 240
  offset: 0, 0
  index: -1
4-1-end
  rotate: true
  xy: 0, 55
  size: 457, 247
  orig: 458, 247
  offset: 1, 0
  index: -1
4-1-frame
  rotate: true
  xy: 248, 55
  size: 457, 247
  orig: 457, 247
  offset: 0, 0
  index: -1
4-1-shadow@zpdd
  rotate: true
  xy: 937, 283
  size: 229, 126
  orig: 229, 126
  offset: 0, 0
  index: -1
4-2-frame
  rotate: true
  xy: 496, 64
  size: 448, 263
  orig: 449, 265
  offset: 1, 1
  index: -1
4-2-shadow@zpdd
  rotate: true
  xy: 972, 55
  size: 227, 141
  orig: 228, 142
  offset: 1, 0
  index: -1
4-2-television
  rotate: false
  xy: 1242, 234
  size: 65, 126
  orig: 66, 128
  offset: 0, 1
  index: -1
4-2-windowL
  rotate: false
  xy: 1064, 299
  size: 112, 213
  orig: 112, 213
  offset: 0, 0
  index: -1
4-2-windowM
  rotate: false
  xy: 894, 37
  size: 77, 234
  orig: 77, 234
  offset: 0, 0
  index: -1
4-2-windowR
  rotate: false
  xy: 760, 258
  size: 72, 254
  orig: 72, 254
  offset: 0, 0
  index: -1
4-3-frame
  rotate: false
  xy: 1177, 361
  size: 149, 151
  orig: 150, 152
  offset: 1, 0
  index: -1
4-3-shadow@zpdd
  rotate: false
  xy: 1191, 22
  size: 75, 76
  orig: 75, 76
  offset: 0, 0
  index: -1
4-3-window
  rotate: true
  xy: 50, 8
  size: 46, 138
  orig: 46, 141
  offset: 0, 0
  index: -1
cabinetL
  rotate: false
  xy: 13, 5
  size: 36, 49
  orig: 36, 49
  offset: 0, 0
  index: -1
cabinetR
  rotate: false
  xy: 1114, 0
  size: 76, 98
  orig: 76, 99
  offset: 0, 1
  index: -1
lamp
  rotate: false
  xy: 0, 3
  size: 12, 51
  orig: 12, 51
  offset: 0, 0
  index: -1
picture
  rotate: true
  xy: 189, 11
  size: 43, 12
  orig: 43, 12
  offset: 0, 0
  index: -1
portrait
  rotate: true
  xy: 202, 34
  size: 20, 57
  orig: 20, 57
  offset: 0, 0
  index: -1
stairs
  rotate: true
  xy: 760, 13
  size: 244, 133
  orig: 245, 136
  offset: 1, 2
  index: -1
