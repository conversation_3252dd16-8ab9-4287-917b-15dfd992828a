using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using System.Text.RegularExpressions;
using DragonCD2.UserSegmentation.Bean;
using DragonCD2.UserSegmentation.Rule.Parameter;
using DragonCD2.UserSegmentation.Rule.Validator;
using Framework;
using Newtonsoft.Json;
using UnityEngine;

namespace DragonCD2.UserSegmentation
{
    public class SkillSetChooseStoryEngine : ISegmentationEngine
    {
        public interface IEngineHelper
        {
            /// <summary>
            /// 获取分层配置表SkillSetChooseStoryJson中的配置导出的Json文本内容
            /// </summary>
            public string StoryRuleConfigText { get; }

            public Dictionary<int, string> DictSkillRuleConfigTexts { get; }

            /// <summary>
            /// 各项目根据自身需求自定义拓展的分层规则条件参数对象列表
            /// </summary>
            FlexibleRuleParam[] RuleParams { get; }
        }

        internal SkillSetChooseStoryEngine()
        {
        }

        private int _chooseStorySegId;

        /// <summary>
        /// 用户行为画像分层Id
        /// </summary>
        public int ChooseStorySegId
        {
            get => _chooseStorySegId;
            private set => _chooseStorySegId = value;
        }
        private int _skillRuleId;
        public int SkillRuleId => _skillRuleId;
        private int[] _skillIdArray;
        public int[] SkillIdArray => _skillIdArray;
        private int[] _skillWeightArray;
        public int[] SkillWeightArray => _skillWeightArray;
        
        private IEngineHelper _helper;

        // 用于规则引擎计算分层的条件参数对象字典
        private readonly Dictionary<string, FlexibleRuleParam> _ruleParamsDic = new();

        IReadOnlyDictionary<string, FlexibleRuleParam> ISegmentationEngine.RuleParamsDictionary => RuleParamsDictionary;

        internal IReadOnlyDictionary<string, FlexibleRuleParam> RuleParamsDictionary => _ruleParamsDic;

        // 分层规则中配置的预设字典
        private readonly Dictionary<string, string> _rulePresetDic = new();

        // 剧本ID的计算规则列表
        private List<ChooseStoryRule> _skillSetChooseRuleList = new();
        // 技能ID的计算规则列表
        private List<ChooseSkillRule> _skillRuleList = new();

        // 分层规则相关的配置及参数等数据是否都已经进行过读取加载
        private bool _isRuleDataLoaded;
        private bool _isSkillRuleDataLoaded;

        /// <summary>
        /// 初始化引擎，计算用户所在分层
        /// </summary>
        public void Init([NotNull] IEngineHelper engineHelper)
        {
            _helper = engineHelper;

            RegisterParam(engineHelper.RuleParams);

            EnsureGroupCalculated();
        }

        /// <summary>
        /// 注册自定义分层条件参数到规则引擎
        /// </summary>
        /// <param name="ruleParams">自定义条件参数对象列表</param>
        private void RegisterParam(params FlexibleRuleParam[] ruleParams)
        {
            foreach (var flexibleRuleParam in ruleParams)
            {
                _ruleParamsDic[flexibleRuleParam.Alias] = flexibleRuleParam;
            }
        }

        /// <summary>
        /// 进行分层计算
        /// </summary>
        private void EnsureGroupCalculated()
        {
            EnsureRuleData();
            // 先计算剧本
            CalculateSegmentation();
            // 再导入技能数据
            EnsureSkillRules();
            // 技能分层的计算放在调用的地方
            //CalculateSkillSegID();
        }

        private void EnsureRuleData()
        {
            if (_isRuleDataLoaded)
            {
                return;
            }

            EnsureRuleParameters();
            EnsureSkillSetChooseRules();

            _isRuleDataLoaded = true;
        }

        private void EnsureRuleParameters()
        {
            if (_isRuleDataLoaded)
            {
                return;
            }

            var assembly = Assembly.GetAssembly(GetType());
            foreach (var type in assembly.GetTypes())
            {
                if (type.IsAbstract || !type.IsSealed || !type.IsSubclassOf(typeof(FlexibleRuleParam)))
                {
                    continue;
                }

                var paramInstance = Activator.CreateInstance(type) as FlexibleRuleParam;

                _ruleParamsDic[paramInstance.Alias] = paramInstance;
            }
        }

        private void EnsureSkillRules()
        {
            if (_helper == null)
            {
                throw new Exception("未设置分层规则引擎必须的IHelper对象!");
            }

            if (_isSkillRuleDataLoaded)
            {
                return;
            }
            string skillRuleJson = "";
            if (_helper.DictSkillRuleConfigTexts.TryGetValue(ChooseStorySegId, out var ruleJson))
            {
                skillRuleJson = ruleJson;
            }

            if (!string.IsNullOrEmpty(skillRuleJson))
            {
                _skillRuleList = JsonConvert.DeserializeObject<List<ChooseSkillRule>>(skillRuleJson);
            }
            if (_skillRuleList == null || _skillRuleList.Count == 0)
            {
                throw new Exception("客户端无尽剧本初始化失败，无法成功加载SkillRulesJson配置数据！");
            }
            foreach (var userGroupRule in _skillRuleList)
            {
                while (IsAnyUnAnalyzedPreset(userGroupRule.Condition))
                {
                    userGroupRule.Condition = AnalyzeExpressionPresets(userGroupRule.Condition);
                }
            }
        }
        private void EnsureSkillSetChooseRules()
        {
            if (_helper == null)
            {
                throw new Exception("未设置分层规则引擎必须的IHelper对象!");
            }

            if (_isRuleDataLoaded)
            {
                return;
            }

            var rulesJson = _helper.StoryRuleConfigText;
            if (string.IsNullOrEmpty(rulesJson))
            {
                throw new Exception("SkillSet Choose Story RulesJson数据解析为空!");
            }

            _skillSetChooseRuleList = JsonConvert.DeserializeObject<List<ChooseStoryRule>>(rulesJson);
            if (_skillSetChooseRuleList == null || _skillSetChooseRuleList.Count == 0)
            {
                throw new Exception("客户端无尽剧本初始化失败，无法成功加载StoryRulesJson配置数据！");
            }

            foreach (var userGroupRule in _skillSetChooseRuleList)
            {
                while (IsAnyUnAnalyzedPreset(userGroupRule.Condition))
                {
                    userGroupRule.Condition = AnalyzeExpressionPresets(userGroupRule.Condition);
                }
            }
        }

        private string AnalyzeExpressionPresets(string expression)
        {
            return Regex.Replace(expression, ISegmentationEngine.RULE_EXPRESSION_PATTERN, Analyze);

            string Analyze(Match match)
            {
                // 根据匹配到的内容生成动态替换文本
                for (int i = 1; i < match.Groups.Count; i++)
                {
                    if (_rulePresetDic.TryGetValue(match.Groups[i].Value, out var presetValue))
                    {
                        return presetValue;
                    }
                }

                return match.Groups[0].Value;
            }
        }

        private bool IsAnyUnAnalyzedPreset(string expression)
        {
            // 谷歌表格导出的规则表达式可能为空
            if (string.IsNullOrEmpty(expression))
            {
                return false;
            }

            var matches = Regex.Matches(expression, ISegmentationEngine.RULE_EXPRESSION_PATTERN);
            if (matches.Count == 0)
            {
                return false;
            }

            foreach (Match matchInfo in matches)
            {
                for (int i = 1; i < matchInfo.Groups.Count; i++)
                {
                    if (_rulePresetDic.ContainsKey(matchInfo.Groups[i].Value))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        public void CalculateSkillSegID()
        {
            foreach (var userGroup in _skillRuleList)
            {
                var isOk = FlexibleRuleValidator.InvokeValidateSkill(this, userGroup);
                if (!isOk)
                {
                    continue;
                }
                _skillRuleId = userGroup.id;
                _skillIdArray = userGroup.skills;
                _skillWeightArray = userGroup.weights;
                CLog.Info($"[无尽剧本] 当前命中的技能规则ID为 [{_skillRuleId}]");
                break;
            }

            if (_skillRuleId == 0)
            {
                // 出现了某种错误，进入保底技能
                _skillRuleId = _skillRuleList[0].id;
                _skillIdArray = _skillRuleList[0].skills;
                _skillWeightArray = _skillRuleList[0].weights;
                CLog.Info($"<color=#ff0000>[无尽剧本] 出现了某种错误，进入保底技能 [{_skillRuleId}]</color>");
            }
        }
        private void CalculateSegmentation()
        {
            foreach (var userGroupRule in _skillSetChooseRuleList)
            {
                var isOk = FlexibleRuleValidator.InvokeValidate(this, userGroupRule);
                if (!isOk)
                {
                    continue;
                }

                ChooseStorySegId = userGroupRule.id;
                CLog.Info($"<color=#00FF33>[无尽剧本] 当前命中的剧本ID为 [{ChooseStorySegId}]</color>");
                break;
            }

            if (ChooseStorySegId == 0)
            {
                // 出现了某种错误，进入保底剧本
                ChooseStorySegId = 2;
                CLog.Info($"<color=#ff0000>[无尽剧本] 出现了某种错误，进入保底剧本 [{ChooseStorySegId}]</color>");
            }
        }
    }
}